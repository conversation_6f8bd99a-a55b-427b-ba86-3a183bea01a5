<template>
  <div v-if="provider" class="provider-detail">
    <div class="detail-header">
      <div class="detail-title">
        <span class="detail-icon">{{ provider.icon || '🤖' }}</span>
        <span class="detail-name">配置 {{ provider.displayName }}</span>
      </div>
      <div class="detail-actions">
        <n-button @click="testConnection" :loading="testing" secondary size="small">
          <template #icon>
            <i class="i-carbon-wifi" />
          </template>
          测试连接
        </n-button>
        <n-button @click="resetConfig" quaternary size="small">
          <template #icon>
            <i class="i-carbon-reset" />
          </template>
        </n-button>
      </div>
    </div>

    <div class="detail-content">
      <!-- API密钥配置 -->
      <div class="detail-section">
        <div class="detail-item-inline">
          <h4>API密钥</h4>
          <n-input
            v-model:value="configForm.apiKey"
            type="password"
            placeholder="输入API密钥..."
            show-password-on="click"
          />
        </div>
      </div>

      <!-- 基础URL配置 -->
      <div class="detail-section">
        <div class="detail-item-inline">
          <h4>基础URL</h4>
          <n-input
            v-model:value="configForm.baseUrl"
            placeholder="https://api.example.com/v1"
          />
        </div>
      </div>

      <!-- 模型选择 -->
      <div class="detail-section">
        <div class="model-header">
          <h4>模型选择</h4>
          <span class="section-subtitle">已选择 {{ selectedModels.length }} 个模型</span>
          <div class="model-actions">
            <n-button @click="selectAllModels" size="small" secondary>全选</n-button>
            <n-button @click="clearAllModels" size="small" secondary>清空</n-button>
          </div>
        </div>

        <div class="model-list">
          <div
            v-for="model in provider.models"
            :key="model.id"
            class="model-item"
            :class="{ 'model-item--selected': selectedModels.includes(model.id) }"
          >
            <n-checkbox
              :checked="selectedModels.includes(model.id)"
              @update:checked="(checked) => toggleModel(model.id, checked)"
            />
            
            <div class="model-info">
              <div class="model-line">
                <span class="model-name">{{ model.displayName }}</span>
                <span class="model-context">{{ formatContextLength(model.contextLength) }}</span>
                <span class="model-price" v-if="model.inputPrice">
                  ${{ model.inputPrice }}/1K tokens
                </span>
                <div class="model-features">
                  <span v-if="model.supportStreaming" class="feature-tag">流式</span>
                  <span v-if="model.supportVision" class="feature-tag">视觉</span>
                  <span v-if="model.supportFunction" class="feature-tag">函数</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useMessage } from 'naive-ui'
import { useSettingsStore } from '@/stores/settings'
import type { AIProvider } from '@/types'

const props = defineProps<{
  provider: AIProvider
}>()

const emit = defineEmits<{
  (e: 'update', providerId: string, updates: Partial<AIProvider>): void
  (e: 'test-connection', providerId: string, success: boolean): void
}>()

const message = useMessage()
const settingsStore = useSettingsStore()

// 响应式数据
const selectedModels = ref<string[]>([])
const testing = ref(false)

// 配置表单
const configForm = ref({
  apiKey: '',
  baseUrl: ''
})

// 方法
const toggleModel = (modelId: string, checked: boolean) => {
  if (checked) {
    selectedModels.value.push(modelId)
  } else {
    const index = selectedModels.value.indexOf(modelId)
    if (index > -1) {
      selectedModels.value.splice(index, 1)
    }
  }
}

const selectAllModels = () => {
  if (props.provider) {
    selectedModels.value = props.provider.models.map(m => m.id)
  }
}

const clearAllModels = () => {
  selectedModels.value = []
}

const formatContextLength = (length: number) => {
  if (length >= 1000000) {
    return `${(length / 1000000).toFixed(1)}M`
  } else if (length >= 1000) {
    return `${(length / 1000).toFixed(0)}K`
  }
  return length.toString()
}

const testConnection = async () => {
  if (!props.provider) return
  
  testing.value = true
  try {
    // 模拟API连接测试
    await new Promise(resolve => setTimeout(resolve, 2000))
    message.success('连接测试成功！')
    emit('test-connection', props.provider.id, true)
  } catch (error) {
    message.error('连接测试失败，请检查配置')
    emit('test-connection', props.provider.id, false)
  } finally {
    testing.value = false
  }
}

const saveConfig = () => {
  if (!props.provider) return

  const updates = {
    apiKey: configForm.value.apiKey,
    baseUrl: configForm.value.baseUrl,
    selectedModels: selectedModels.value
  }

  settingsStore.updateProvider(props.provider.id, updates)
  emit('update', props.provider.id, updates)

  message.success('配置已保存')
}

const resetConfig = () => {
  if (!props.provider) return
  
  configForm.value.apiKey = ''
  configForm.value.baseUrl = ''
  selectedModels.value = []
  
  message.info('配置已重置')
}

// 监听provider变化，更新表单
watch(() => props.provider, (newProvider) => {
  if (newProvider) {
    configForm.value.apiKey = newProvider.apiKey || ''
    configForm.value.baseUrl = newProvider.baseUrl || ''
    // 只有当提供商有保存的选中模型配置时才使用，否则默认为空
    selectedModels.value = newProvider.selectedModels || []
  }
}, { immediate: true })

// 监听配置变化，自动保存
watch(configForm, (newConfig) => {
  if (props.provider) {
    const updates = {
      apiKey: newConfig.apiKey,
      baseUrl: newConfig.baseUrl
    }
    settingsStore.updateProvider(props.provider.id, updates)
  }
}, { deep: true })

// 监听选中模型变化，自动保存
watch(selectedModels, (newSelectedModels) => {
  if (props.provider) {
    const updates = {
      selectedModels: newSelectedModels
    }
    settingsStore.updateProvider(props.provider.id, updates)
  }
}, { deep: true })
</script>

<style scoped>
.provider-detail {
  background: white;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.detail-header {
  padding: 6px 12px;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.detail-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.detail-icon {
  font-size: 16px;
}

.detail-name {
  font-size: 12px;
  font-weight: 500;
  color: #1f2937;
}

.detail-actions {
  display: flex;
  gap: 4px;
}

.detail-actions .n-button {
  font-size: 11px;
  padding: 2px 6px;
  height: 24px;
}

.detail-content {
  padding: 12px;
  flex: 1;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: 12px;
}

.detail-section h4 {
  font-size: 12px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 6px;
}



.detail-item {
  margin-bottom: 4px;
}

.detail-item-inline {
  display: flex;
  align-items: center;
  gap: 2px;
  margin-bottom: 4px;
}

.detail-item-inline h4 {
  margin: 0;
  min-width: 50px;
  flex-shrink: 0;
  font-size: 12px;
  font-weight: 500;
  color: #374151;
}

.detail-item-inline .n-input {
  flex: 1;
}

.detail-item-inline .n-input :deep(.n-input__input-el) {
  font-size: 12px;
  height: 28px;
  padding: 4px 8px;
}

.detail-item-inline .n-input :deep(.n-input__placeholder) {
  font-size: 12px;
}

.detail-hint {
  display: block;
  margin-top: 4px;
  font-size: 14px;
  color: #9ca3af;
}

.model-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.model-header h4 {
  margin: 0;
  flex-shrink: 0;
}

.section-subtitle {
  font-size: 11px;
  color: #6b7280;
  flex: 1;
}

.model-actions {
  display: flex;
  gap: 2px;
  flex-shrink: 0;
}

.model-actions .n-button {
  font-size: 11px;
  padding: 1px 4px;
  height: 20px;
}

.model-list {
  display: flex;
  flex-direction: column;
  gap: 2px;
  height: 150px;
  overflow-y: auto;
  border: 1px solid #e5e7eb;
  border-radius: 3px;
  padding: 6px;
  background: #fafafa;
  box-sizing: border-box;
}

.model-item {
  display: flex;
  align-items: flex-start;
  gap: 6px;
  padding: 6px;
  border: 1px solid #e5e7eb;
  border-radius: 3px;
  transition: all 0.2s ease;
  background: white;
}

.model-item:hover {
  background: #f8fafc;
  border-color: #cbd5e1;
}

.model-item--selected {
  background: #f8fafc;
  border-color: #e5e7eb;
}

.model-info {
  flex: 1;
}

.model-line {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 11px;
}

.model-name {
  font-size: 12px;
  font-weight: 500;
  color: #1f2937;
  flex-shrink: 0;
}

.model-context {
  background: #f3f4f6;
  padding: 1px 4px;
  border-radius: 3px;
  font-size: 10px;
  color: #6b7280;
  flex-shrink: 0;
}

.model-price {
  color: #059669;
  font-size: 10px;
  flex-shrink: 0;
}

.model-features {
  display: flex;
  gap: 3px;
  flex-shrink: 0;
}

.feature-tag {
  background: #dbeafe;
  color: #1e40af;
  padding: 1px 4px;
  border-radius: 2px;
  font-size: 9px;
  line-height: 1.2;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .detail-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .detail-actions {
    width: 100%;
    justify-content: flex-end;
  }
  
  .model-details {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}
</style>
