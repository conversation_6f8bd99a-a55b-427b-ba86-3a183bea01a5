<template>
  <div class="chat-input">
    <!-- 输入框容器 -->
    <div class="input-container">
      <!-- 附件预览 -->
      <div v-if="attachments.length > 0" class="attachments-preview">
        <div
          v-for="(attachment, index) in attachments"
          :key="attachment.id"
          class="attachment-item"
        >
          <div class="attachment-info">
            <n-icon :component="getFileIcon(attachment.type)" />
            <span class="attachment-name">{{ attachment.name }}</span>
            <span class="attachment-size">{{ formatFileSize(attachment.size) }}</span>
          </div>
          <n-button
            quaternary
            circle
            size="tiny"
            @click="removeAttachment(index)"
          >
            <template #icon>
              <n-icon :component="XIcon" />
            </template>
          </n-button>
        </div>
      </div>

      <!-- 主输入区域 -->
      <div class="main-input">
        <!-- 输入框 -->
        <n-input
          ref="inputRef"
          v-model:value="inputValue"
          type="textarea"
          :placeholder="placeholder"
          :disabled="disabled"
          :autosize="{ minRows: 1, maxRows: 8 }"
          :show-count="showCount"
          :maxlength="maxLength"
          class="message-input"
          @keydown="handleKeydown"
          @paste="handlePaste"
        />

        <!-- 操作按钮 -->
        <div class="input-actions">
          <!-- 附件按钮 -->
          <n-button
            quaternary
            circle
            @click="selectFiles"
            :disabled="disabled"
            title="添加附件"
          >
            <template #icon>
              <n-icon :component="PaperclipIcon" />
            </template>
          </n-button>

          <!-- 表情按钮 -->
          <n-popover trigger="click" placement="top-start">
            <template #trigger>
              <n-button
                quaternary
                circle
                :disabled="disabled"
                title="插入表情"
              >
                <template #icon>
                  <n-icon :component="SmileIcon" />
                </template>
              </n-button>
            </template>
            <div class="emoji-picker">
              <div
                v-for="emoji in commonEmojis"
                :key="emoji"
                class="emoji-item"
                @click="insertEmoji(emoji)"
              >
                {{ emoji }}
              </div>
            </div>
          </n-popover>

          <!-- 发送按钮 -->
          <n-button
            type="primary"
            circle
            :disabled="!canSend"
            :loading="disabled"
            @click="handleSend"
            title="发送消息 (Ctrl+Enter)"
          >
            <template #icon>
              <n-icon :component="SendIcon" />
            </template>
          </n-button>
        </div>
      </div>


    </div>

    <!-- 隐藏的文件输入 -->
    <input
      ref="fileInputRef"
      type="file"
      multiple
      accept="image/*,.pdf,.doc,.docx,.txt,.md"
      style="display: none"
      @change="handleFileSelect"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick } from 'vue'
import { useMessage } from 'naive-ui'
import {
  Send as SendIcon,
  Paperclip as PaperclipIcon,
  Smile as SmileIcon,
  X as XIcon,
  File as FileIcon,
  Image as ImageIcon,
  FileText as FileTextIcon
} from 'lucide-vue-next'
import type { FileAttachment } from '@/types'

const props = withDefaults(defineProps<{
  placeholder?: string
  disabled?: boolean
  maxLength?: number
  showCount?: boolean
}>(), {
  placeholder: '输入消息... (Ctrl+Enter发送，Shift+Enter换行)'
})

const emit = defineEmits<{
  send: [content: string, attachments?: FileAttachment[]]
}>()

const message = useMessage()

// 响应式数据
const inputValue = ref('')
const attachments = ref<FileAttachment[]>([])
const inputRef = ref()
const fileInputRef = ref()

// 常用表情
const commonEmojis = [
  '😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣',
  '😊', '😇', '🙂', '🙃', '😉', '😌', '😍', '🥰',
  '😘', '😗', '😙', '😚', '😋', '😛', '😝', '😜',
  '🤪', '🤨', '🧐', '🤓', '😎', '🤩', '🥳', '😏',
  '👍', '👎', '👌', '✌️', '🤞', '🤟', '🤘', '🤙',
  '💪', '🙏', '✨', '🎉', '🎊', '💯', '🔥', '⚡'
]

// 计算属性
const canSend = computed(() => {
  return !props.disabled && (inputValue.value.trim() || attachments.value.length > 0)
})

// 方法
const handleKeydown = (event: KeyboardEvent) => {
  // Ctrl+Enter 发送消息
  if (event.ctrlKey && event.key === 'Enter') {
    event.preventDefault()
    handleSend()
  }
  
  // Shift+Enter 换行（默认行为）
  if (event.shiftKey && event.key === 'Enter') {
    // 让默认行为处理换行
    return
  }
  
  // 单独的Enter键在移动端发送，桌面端换行
  if (event.key === 'Enter' && !event.shiftKey && !event.ctrlKey) {
    // 检测是否为移动设备或触摸设备
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) || 
                     ('ontouchstart' in window)
    
    if (isMobile) {
      event.preventDefault()
      handleSend()
    }
    // 桌面端让默认行为处理换行
  }
}

const handleSend = () => {
  if (!canSend.value) return
  
  const content = inputValue.value.trim()
  const currentAttachments = [...attachments.value]
  
  if (content || currentAttachments.length > 0) {
    emit('send', content, currentAttachments.length > 0 ? currentAttachments : undefined)
    
    // 清空输入
    inputValue.value = ''
    attachments.value = []
    
    // 重新聚焦输入框
    nextTick(() => {
      inputRef.value?.focus()
    })
  }
}

const selectFiles = () => {
  fileInputRef.value?.click()
}

const handleFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement
  const files = target.files
  
  if (files) {
    for (let i = 0; i < files.length; i++) {
      const file = files[i]
      
      // 检查文件大小（限制10MB）
      if (file.size > 10 * 1024 * 1024) {
        message.error(`文件 ${file.name} 超过10MB限制`)
        continue
      }
      
      const attachment: FileAttachment = {
        id: crypto.randomUUID(),
        name: file.name,
        type: file.type,
        size: file.size
      }
      
      // 如果是图片，生成缩略图
      if (file.type.startsWith('image/')) {
        const reader = new FileReader()
        reader.onload = (e) => {
          attachment.thumbnail = e.target?.result as string
        }
        reader.readAsDataURL(file)
      }
      
      attachments.value.push(attachment)
    }
  }
  
  // 清空文件输入
  target.value = ''
}

const removeAttachment = (index: number) => {
  attachments.value.splice(index, 1)
}

const handlePaste = (event: ClipboardEvent) => {
  const items = event.clipboardData?.items
  if (!items) return
  
  for (let i = 0; i < items.length; i++) {
    const item = items[i]
    
    // 处理粘贴的图片
    if (item.type.startsWith('image/')) {
      const file = item.getAsFile()
      if (file) {
        const attachment: FileAttachment = {
          id: crypto.randomUUID(),
          name: `粘贴的图片_${Date.now()}.png`,
          type: file.type,
          size: file.size
        }
        
        // 生成缩略图
        const reader = new FileReader()
        reader.onload = (e) => {
          attachment.thumbnail = e.target?.result as string
        }
        reader.readAsDataURL(file)
        
        attachments.value.push(attachment)
      }
    }
  }
}

const insertEmoji = (emoji: string) => {
  const cursorPosition = inputRef.value?.textareaElRef?.selectionStart || inputValue.value.length
  const before = inputValue.value.slice(0, cursorPosition)
  const after = inputValue.value.slice(cursorPosition)
  
  inputValue.value = before + emoji + after
  
  // 重新聚焦并设置光标位置
  nextTick(() => {
    inputRef.value?.focus()
    const newPosition = cursorPosition + emoji.length
    inputRef.value?.textareaElRef?.setSelectionRange(newPosition, newPosition)
  })
}

const getFileIcon = (type: string) => {
  if (type.startsWith('image/')) return ImageIcon
  if (type.includes('text') || type.includes('document')) return FileTextIcon
  return FileIcon
}

const formatFileSize = (bytes: number): string => {
  if (bytes < 1024) return `${bytes} B`
  if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`
  return `${(bytes / (1024 * 1024)).toFixed(1)} MB`
}
</script>

<style scoped>
.chat-input {
  width: 100%;
}

.input-container {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background: #ffffff;
  transition: all 0.2s ease;
}

.input-container:focus-within {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* 附件预览 */
.attachments-preview {
  padding: 12px 16px 0;
  border-bottom: 1px solid #f3f4f6;
}

.attachment-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  background: #f9fafb;
  border-radius: 8px;
  margin-bottom: 8px;
}

.attachment-info {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
  min-width: 0;
}

.attachment-name {
  font-size: 14px;
  color: #374151;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.attachment-size {
  font-size: 12px;
  color: #9ca3af;
  flex-shrink: 0;
}

/* 主输入区域 */
.main-input {
  display: flex;
  align-items: flex-end;
  gap: 12px;
  padding: 16px;
}

.message-input {
  flex: 1;
}

.message-input :deep(.n-input__textarea-el) {
  resize: none;
  font-size: 14px;
  line-height: 1.5;
}

.input-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
}

/* 表情选择器 */
.emoji-picker {
  display: grid;
  grid-template-columns: repeat(8, 1fr);
  gap: 4px;
  padding: 8px;
  max-width: 280px;
}

.emoji-item {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  cursor: pointer;
  font-size: 18px;
  transition: background-color 0.2s ease;
}

.emoji-item:hover {
  background: #f3f4f6;
}



/* 响应式设计 */
@media (max-width: 768px) {
  .main-input {
    padding: 12px;
    gap: 8px;
  }
  
  .input-actions {
    gap: 4px;
  }
  

  
  .emoji-picker {
    grid-template-columns: repeat(6, 1fr);
    max-width: 240px;
  }
  
  .emoji-item {
    width: 28px;
    height: 28px;
    font-size: 16px;
  }
}
</style>
