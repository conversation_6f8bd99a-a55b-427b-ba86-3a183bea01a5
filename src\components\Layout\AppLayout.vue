<template>
  <n-layout has-sider class="h-full app-layout">
    <!-- 侧边栏 -->
    <n-layout-sider
      bordered
      collapse-mode="width"
      :collapsed-width="48"
      :width="240"
      :collapsed="sidebarCollapsed"
      show-trigger
      @collapse="sidebarCollapsed = true"
      @expand="sidebarCollapsed = false"
      class="sidebar"
    >
      <ChatSidebar :collapsed="sidebarCollapsed" />
    </n-layout-sider>
    
    <!-- 主内容区域 -->
    <n-layout>
      <!-- 顶部标题栏 -->
      <n-layout-header bordered class="header">
        <AppHeader />
      </n-layout-header>
      
      <!-- 主内容 -->
      <n-layout-content class="main-content">
        <router-view v-slot="{ Component }">
          <transition name="fade" mode="out-in">
            <component :is="Component" />
          </transition>
        </router-view>
      </n-layout-content>
    </n-layout>

    <!-- 设置模态窗口 -->
    <SettingsModal ref="settingsModalRef" />
  </n-layout>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import ChatSidebar from '@/components/Sidebar/ChatSidebar.vue'
import AppHeader from '@/components/Layout/AppHeader.vue'
import SettingsModal from '@/components/Settings/SettingsModal.vue'

const sidebarCollapsed = ref(false)
const settingsModalRef = ref()

// 监听设置事件
const handleOpenSettings = () => {
  if (settingsModalRef.value) {
    settingsModalRef.value.openModal()
  }
}

// 组件挂载和卸载
onMounted(() => {
  window.addEventListener('open-settings-sidebar', handleOpenSettings)
})

onUnmounted(() => {
  window.removeEventListener('open-settings-sidebar', handleOpenSettings)
})
</script>

<style scoped>
.sidebar {
  background: #fafafa;
  border-right: 1px solid #e5e7eb;
  height: 100%;
}

.sidebar :deep(.n-layout-sider-trigger) {
  position: absolute;
  top: 50%;
  right: -12px;
  transform: translateY(-50%);
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: #ffffff;
  border: 1px solid #e5e7eb;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  z-index: 10;
  transition: all 0.2s ease;
}

.sidebar :deep(.n-layout-sider-trigger):hover {
  background: #f9fafb;
  border-color: #d1d5db;
}

.app-layout {
  width: 100%;
  height: 100%;
}

.header {
  height: 48px;
  display: flex;
  align-items: center;
  padding: 0 16px;
  background: #ffffff;
  border-bottom: 1px solid #e5e7eb;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.main-content {
  height: calc(100vh - 48px);
  overflow: hidden;
  position: relative;
}

/* 过渡动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
