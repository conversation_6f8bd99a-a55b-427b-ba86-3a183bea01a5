import { defineStore } from 'pinia'
import { ref } from 'vue'
import type { AppSettings, ChatSettings, AIProvider, Assistant } from '@/types'

export const useSettingsStore = defineStore('settings', () => {
  // 应用设置
  const app = ref<AppSettings>({
    theme: 'auto',
    language: 'zh-CN',
    fontSize: 14,
    autoScroll: true,
    showLineNumbers: true,
    enableMarkdown: true,
    enableCodeHighlight: true,
    enableMath: true,
    saveHistory: true,
    maxHistoryDays: 30
  })

  // 聊天设置
  const chat = ref<ChatSettings>({
    temperature: 0.7,
    maxTokens: 2048,
    topP: 1.0,
    frequencyPenalty: 0,
    presencePenalty: 0,
    systemPrompt: '你是一个有用的AI助手，请用中文回答问题。'
  })

  // AI提供商配置
  const providers = ref<AIProvider[]>([
    {
      id: 'deepseek',
      name: 'deepseek',
      displayName: 'DeepSeek AI',
      enabled: true,
      icon: '🔍',
      baseUrl: 'https://api.deepseek.com/v1',
      models: [
        {
          id: 'deepseek-chat',
          name: 'deepseek-chat',
          displayName: 'DeepSeek Chat',
          contextLength: 32768,
          inputPrice: 0.0014,
          outputPrice: 0.0028,
          supportStreaming: true,
          supportVision: false,
          supportFunction: true
        },
        {
          id: 'deepseek-reasoner',
          name: 'deepseek-reasoner',
          displayName: 'DeepSeek Reasoner',
          contextLength: 32768,
          inputPrice: 0.0055,
          outputPrice: 0.0280,
          supportStreaming: true,
          supportVision: false,
          supportFunction: true,
          isReasoningModel: true // 标识为思考模型
        }
      ]
    },
    {
      id: 'openai',
      name: 'openai',
      displayName: 'OpenAI',
      enabled: false,
      icon: '🤖',
      baseUrl: 'https://api.openai.com/v1',
      models: [
        {
          id: 'gpt-4',
          name: 'gpt-4',
          displayName: 'GPT-4',
          contextLength: 8192,
          inputPrice: 0.03,
          outputPrice: 0.06,
          supportStreaming: true,
          supportVision: false,
          supportFunction: true
        },
        {
          id: 'gpt-3.5-turbo',
          name: 'gpt-3.5-turbo',
          displayName: 'GPT-3.5 Turbo',
          contextLength: 4096,
          inputPrice: 0.001,
          outputPrice: 0.002,
          supportStreaming: true,
          supportVision: false,
          supportFunction: true
        }
      ]
    },
    {
      id: 'anthropic',
      name: 'anthropic',
      displayName: 'Anthropic',
      enabled: false,
      icon: '🧠',
      baseUrl: 'https://api.anthropic.com/v1',
      models: [
        {
          id: 'claude-3-opus',
          name: 'claude-3-opus-20240229',
          displayName: 'Claude 3 Opus',
          contextLength: 200000,
          inputPrice: 0.015,
          outputPrice: 0.075,
          supportStreaming: true,
          supportVision: true,
          supportFunction: true
        },
        {
          id: 'claude-3-sonnet',
          name: 'claude-3-sonnet-20240229',
          displayName: 'Claude 3 Sonnet',
          contextLength: 200000,
          inputPrice: 0.003,
          outputPrice: 0.015,
          supportStreaming: true,
          supportVision: true,
          supportFunction: true
        }
      ]
    },
    {
      id: 'google',
      name: 'google',
      displayName: 'Google Gemini',
      enabled: false,
      icon: '💎',
      baseUrl: 'https://generativelanguage.googleapis.com/v1',
      models: [
        {
          id: 'gemini-1.5-pro',
          name: 'gemini-1.5-pro',
          displayName: 'Gemini 1.5 Pro',
          contextLength: 2097152,
          inputPrice: 0.00125,
          outputPrice: 0.00375,
          supportStreaming: true,
          supportVision: true,
          supportFunction: true
        },
        {
          id: 'gemini-1.5-flash',
          name: 'gemini-1.5-flash',
          displayName: 'Gemini 1.5 Flash',
          contextLength: 1048576,
          inputPrice: 0.000075,
          outputPrice: 0.0003,
          supportStreaming: true,
          supportVision: true,
          supportFunction: true
        },
        {
          id: 'gemini-1.5-pro',
          name: 'gemini-1.5-pro',
          displayName: 'Gemini 1.5 Pro',
          contextLength: 2097152,
          inputPrice: 0.00125,
          outputPrice: 0.00375,
          supportStreaming: true,
          supportVision: true,
          supportFunction: true
        },
        {
          id: 'gemini-1.5-flash',
          name: 'gemini-1.5-flash',
          displayName: 'Gemini 1.5 Flash',
          contextLength: 1048576,
          inputPrice: 0.000075,
          outputPrice: 0.0003,
          supportStreaming: true,
          supportVision: true,
          supportFunction: true
        },
        {
          id: 'gemini-pro',
          name: 'gemini-pro',
          displayName: 'Gemini Pro',
          contextLength: 32768,
          inputPrice: 0.0005,
          outputPrice: 0.0015,
          supportStreaming: true,
          supportVision: false,
          supportFunction: true
        }
      ]
    }
  ])

  // 助手配置
  const assistants = ref<Assistant[]>([
    {
      id: 'default',
      name: '通用助手',
      description: '一个通用的AI助手，可以回答各种问题',
      avatar: '🤖',
      systemPrompt: '你是一个有用的AI助手，请用中文回答问题。',
      isBuiltIn: true,
      isEnabled: true,
      category: '通用',
      tags: ['通用', '问答'],
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 'programmer',
      name: '编程助手',
      description: '专业的编程助手，擅长代码编写和调试',
      avatar: '👨‍💻',
      systemPrompt: '你是一个专业的编程助手，擅长多种编程语言，可以帮助用户编写、调试和优化代码。请提供清晰的代码示例和详细的解释。',
      isBuiltIn: true,
      isEnabled: true,
      category: '编程',
      tags: ['编程', '代码', '调试'],
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 'translator',
      name: '翻译助手',
      description: '专业的多语言翻译助手',
      avatar: '🌍',
      systemPrompt: '你是一个专业的翻译助手，可以准确翻译多种语言。请提供自然流畅的翻译，并在必要时解释文化背景。',
      isBuiltIn: true,
      isEnabled: true,
      category: '语言',
      tags: ['翻译', '语言', '多语言'],
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 'writer',
      name: '写作助手',
      description: '创意写作和文案编辑助手',
      avatar: '✍️',
      systemPrompt: '你是一个专业的写作助手，擅长创意写作、文案编辑和内容优化。请提供有创意且符合要求的文本内容。',
      isBuiltIn: true,
      isEnabled: true,
      category: '写作',
      tags: ['写作', '创意', '文案'],
      createdAt: new Date(),
      updatedAt: new Date()
    }
  ])

  // 更新应用设置
  const updateAppSettings = (newSettings: Partial<AppSettings>) => {
    app.value = { ...app.value, ...newSettings }
    saveSettings()
  }

  // 更新聊天设置
  const updateChatSettings = (newSettings: Partial<ChatSettings>) => {
    chat.value = { ...chat.value, ...newSettings }
    saveSettings()
  }

  // 更新提供商设置
  const updateProvider = (providerId: string, updates: Partial<AIProvider>) => {
    const index = providers.value.findIndex(p => p.id === providerId)
    if (index !== -1) {
      providers.value[index] = { ...providers.value[index], ...updates }
      saveSettings()
    }
  }

  // 添加自定义助手
  const addAssistant = (assistant: Omit<Assistant, 'id' | 'createdAt' | 'updatedAt'>) => {
    const newAssistant: Assistant = {
      ...assistant,
      id: crypto.randomUUID(),
      createdAt: new Date(),
      updatedAt: new Date()
    }
    assistants.value.push(newAssistant)
    saveSettings()
    return newAssistant
  }

  // 更新助手
  const updateAssistant = (assistantId: string, updates: Partial<Assistant>) => {
    const index = assistants.value.findIndex(a => a.id === assistantId)
    if (index !== -1) {
      assistants.value[index] = {
        ...assistants.value[index],
        ...updates,
        updatedAt: new Date()
      }
      saveSettings()
    }
  }

  // 删除助手
  const deleteAssistant = (assistantId: string) => {
    const index = assistants.value.findIndex(a => a.id === assistantId)
    if (index !== -1 && !assistants.value[index].isBuiltIn) {
      assistants.value.splice(index, 1)
      saveSettings()
    }
  }

  // 保存设置到本地存储
  const saveSettings = () => {
    const settings = {
      app: app.value,
      chat: chat.value,
      providers: providers.value,
      assistants: assistants.value
    }
    localStorage.setItem('ai-client-settings', JSON.stringify(settings))
  }

  // 从本地存储加载设置
  const loadSettings = () => {
    try {
      const saved = localStorage.getItem('ai-client-settings')
      if (saved) {
        const settings = JSON.parse(saved)
        if (settings.app) app.value = { ...app.value, ...settings.app }
        if (settings.chat) chat.value = { ...chat.value, ...settings.chat }
        if (settings.providers) {
          // 合并提供商设置，保留默认配置
          providers.value = providers.value.map(defaultProvider => {
            const savedProvider = settings.providers.find((p: any) => p.id === defaultProvider.id)
            return savedProvider ? { ...defaultProvider, ...savedProvider } : defaultProvider
          })
        }
        if (settings.assistants) {
          // 合并助手设置，保留内置助手
          const builtInAssistants = assistants.value.filter(a => a.isBuiltIn)
          const customAssistants = settings.assistants.filter((a: any) => !a.isBuiltIn)
          assistants.value = [...builtInAssistants, ...customAssistants]
        }
      }
    } catch (error) {
      console.error('加载设置失败:', error)
    }
  }

  // 重置设置
  const resetSettings = () => {
    localStorage.removeItem('ai-client-settings')
    // 重新初始化默认值
    location.reload()
  }

    // 初始化函数
  const initialize = () => {
    loadSettings()
  }

  // 初始化
  initialize()

  return {
    app,
    chat,
    providers,
    assistants,
    updateAppSettings,
    updateChatSettings,
    updateProvider,
    addAssistant,
    updateAssistant,
    deleteAssistant,
    saveSettings,
    loadSettings,
    resetSettings,
    initialize
  }
})
