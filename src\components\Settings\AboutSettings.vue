<template>
  <div class="about-settings">
    <div class="settings-section">
      <div class="section-content">
        <div class="app-info">
          <div class="app-icon">🤖</div>
          <h2>AI桌面客户端</h2>
          <p class="version">版本 1.0.0</p>
          <p class="description">
            基于Tauri + Vue3构建的高性能AI桌面应用，支持多个AI提供商，提供专业的对话体验。
          </p>
        </div>

        <div class="tech-stack">
          <h3>技术栈</h3>
          <div class="tech-grid">
            <div class="tech-item">
              <div class="tech-icon">⚡</div>
              <div class="tech-info">
                <strong>前端框架</strong>
                <span>Vue 3 + TypeScript</span>
              </div>
            </div>
            <div class="tech-item">
              <div class="tech-icon">🎨</div>
              <div class="tech-info">
                <strong>UI组件</strong>
                <span>Naive UI</span>
              </div>
            </div>
            <div class="tech-item">
              <div class="tech-icon">🦀</div>
              <div class="tech-info">
                <strong>桌面框架</strong>
                <span>Tauri + Rust</span>
              </div>
            </div>
            <div class="tech-item">
              <div class="tech-icon">🔧</div>
              <div class="tech-info">
                <strong>构建工具</strong>
                <span>Vite + UnoCSS</span>
              </div>
            </div>
          </div>
        </div>

        <div class="features">
          <h3>主要功能</h3>
          <div class="feature-list">
            <div class="feature-item">
              <i class="i-carbon-checkmark-filled" />
              <span>多AI提供商支持</span>
            </div>
            <div class="feature-item">
              <i class="i-carbon-checkmark-filled" />
              <span>实时流式对话</span>
            </div>
            <div class="feature-item">
              <i class="i-carbon-checkmark-filled" />
              <span>Markdown渲染</span>
            </div>
            <div class="feature-item">
              <i class="i-carbon-checkmark-filled" />
              <span>代码语法高亮</span>
            </div>
            <div class="feature-item">
              <i class="i-carbon-checkmark-filled" />
              <span>对话历史管理</span>
            </div>
            <div class="feature-item">
              <i class="i-carbon-checkmark-filled" />
              <span>自定义助手</span>
            </div>
          </div>
        </div>

        <div class="actions">
          <n-button @click="checkUpdate" type="primary">
            <template #icon>
              <i class="i-carbon-update-now" />
            </template>
            检查更新
          </n-button>
          <n-button @click="openGithub" secondary>
            <template #icon>
              <i class="i-carbon-logo-github" />
            </template>
            GitHub
          </n-button>
          <n-button @click="openDocs" secondary>
            <template #icon>
              <i class="i-carbon-document" />
            </template>
            文档
          </n-button>
        </div>

        <div class="copyright">
          <p>&copy; 2024 AI桌面客户端. All rights reserved.</p>
          <p>Built with ❤️ using modern web technologies</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useMessage } from 'naive-ui'
// import { open } from '@tauri-apps/api/shell'

const message = useMessage()

// 方法
const checkUpdate = () => {
  message.info('当前已是最新版本')
}

const openGithub = async () => {
  try {
    // 在实际的Tauri环境中使用 shell API
    // await open('https://github.com/your-repo/ai-desktop-client')
    window.open('https://github.com/your-repo/ai-desktop-client', '_blank')
  } catch (error) {
    message.error('无法打开链接')
  }
}

const openDocs = async () => {
  try {
    // 在实际的Tauri环境中使用 shell API
    // await open('https://docs.your-app.com')
    window.open('https://docs.your-app.com', '_blank')
  } catch (error) {
    message.error('无法打开链接')
  }
}
</script>

<style scoped>
.about-settings {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.settings-section {
  background: white;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  overflow: hidden;
}

.section-content {
  padding: 40px;
  text-align: center;
}

.app-info {
  margin-bottom: 40px;
}

.app-icon {
  font-size: 64px;
  margin-bottom: 16px;
}

.app-info h2 {
  font-size: 28px;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 8px;
}

.version {
  font-size: 16px;
  color: #6b7280;
  margin-bottom: 16px;
}

.description {
  font-size: 16px;
  color: #9ca3af;
  line-height: 1.6;
  max-width: 500px;
  margin: 0 auto;
}

.tech-stack {
  margin-bottom: 40px;
}

.tech-stack h3 {
  font-size: 20px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 20px;
}

.tech-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  max-width: 600px;
  margin: 0 auto;
}

.tech-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
  text-align: left;
}

.tech-icon {
  font-size: 24px;
}

.tech-info strong {
  display: block;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 2px;
}

.tech-info span {
  font-size: 12px;
  color: #6b7280;
}

.features {
  margin-bottom: 40px;
}

.features h3 {
  font-size: 20px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 20px;
}

.feature-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
  max-width: 600px;
  margin: 0 auto;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 8px;
  text-align: left;
  font-size: 14px;
  color: #374151;
}

.feature-item i {
  color: #10b981;
  font-size: 16px;
}

.actions {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-bottom: 40px;
  flex-wrap: wrap;
}

.copyright {
  border-top: 1px solid #e5e7eb;
  padding-top: 24px;
  font-size: 12px;
  color: #9ca3af;
}

.copyright p {
  margin: 4px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .section-content {
    padding: 24px;
  }
  
  .tech-grid {
    grid-template-columns: 1fr;
  }
  
  .feature-list {
    grid-template-columns: 1fr;
  }
  
  .actions {
    flex-direction: column;
    align-items: center;
  }
}
</style>
