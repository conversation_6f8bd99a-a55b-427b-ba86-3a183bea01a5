<template>
  <div class="chat-view">
    <!-- 聊天消息区域 -->
    <div class="chat-messages" ref="messagesContainer">
      <!-- 智能滚动容器 -->
      <n-scrollbar class="messages-scrollbar" ref="scrollbarRef">
        <!-- 欢迎消息 -->
        <div v-if="currentMessages.length === 0" class="welcome-message">
        <div class="welcome-icon">🤖</div>
        <h2 class="welcome-title">欢迎使用AI桌面客户端</h2>
        <p class="welcome-description">
          这是一个基于Tauri + Vue3构建的高性能AI桌面应用。
          <br>
          请在下方输入您的问题，开始对话吧！
        </p>

        <!-- 快速开始建议 -->
        <div class="quick-suggestions">
          <h3>快速开始：</h3>
          <div class="suggestion-buttons">
            <n-button
              v-for="suggestion in quickSuggestions"
              :key="suggestion.text"
              @click="sendSuggestion(suggestion.text)"
              class="suggestion-btn"
              secondary
            >
              {{ suggestion.icon }} {{ suggestion.text }}
            </n-button>
          </div>
        </div>

        <!-- 性能设置 -->
        <div class="performance-settings">
          <n-space>
            <n-switch
              v-model:value="showPerformanceStats"
              @update:value="updatePerformanceSettings"
            >
              <template #checked>显示统计</template>
              <template #unchecked>隐藏统计</template>
            </n-switch>
          </n-space>
        </div>
      </div>

      <!-- 高性能虚拟消息列表 -->
      <div v-else class="messages-container">


        <VirtualMessageList
          ref="virtualMessageListRef"
          :messages="currentMessages"
          :is-loading="isLoading"
          :is-streaming="isStreaming"
          :streaming-message-id="streamingMessageId"
          :item-height="120"
          :show-stats="showPerformanceStats"
          @regenerate="handleRegenerate"
          @load-more="loadMoreMessages"
        />

        <!-- 加载指示器 -->
        <div v-if="isLoading && !isStreaming" class="loading-indicator">
          <n-spin size="small" />
          <span>AI正在思考中...</span>
        </div>

        <!-- 流式输入指示器 -->
        <div v-if="isStreaming" class="streaming-indicator">
          <div class="streaming-dots">
            <span></span>
            <span></span>
            <span></span>
          </div>
          <span>正在接收回复...</span>
        </div>
      </div>
      </n-scrollbar>

      <!-- 滚动到底部按钮 -->
      <div v-if="!autoScroll" class="scroll-to-bottom">
        <n-button circle size="small" @click="handleScrollToBottom" title="滚动到底部">
          ⬇️
        </n-button>
      </div>
    </div>

    <!-- 性能监控面板 -->
    <PerformancePanel
      v-if="showPerformanceStats"
      :message-count="currentMessages.length"
      :async-renderer="asyncRenderer"
      :virtual-scroll="virtualMessageListRef"
      @clear-cache="handleClearCache"
      @reset-stats="handleResetStats"
    />

    <!-- 输入区域 -->
    <div class="chat-input-area">
      <div class="simple-input-container">
        <n-input
          v-model:value="inputValue"
          type="textarea"
          :placeholder="inputPlaceholder"
          :disabled="isLoading"
          :autosize="{ minRows: 1, maxRows: 4 }"
          @keydown.enter.exact.prevent="handleSend"
          class="message-input"
        />
        <n-button
          type="primary"
          :disabled="!inputValue.trim() || isLoading"
          @click="handleSend"
          class="send-button"
        >
          发送
        </n-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useChatStore } from '../stores/chat'
import VirtualMessageList from '../components/Chat/VirtualMessageList.vue'
import PerformancePanel from '../components/Chat/PerformancePanel.vue'

const chatStore = useChatStore()

// 初始化chatStore
onMounted(() => {
  console.log('🚀 ChatView初始化...')
  chatStore.initialize()
  console.log('✅ ChatView初始化完成')
})

// 响应式数据
const messagesContainer = ref<HTMLElement>()
const scrollbarRef = ref<any>()
const virtualMessageListRef = ref<any>()
const enableOptimizations = ref(true) // 固定启用高性能模式
const showPerformanceStats = ref(false)

const inputValue = ref('')
const autoScroll = ref(true)
const asyncRenderer = ref<any>(null)

// 从store获取数据
const currentMessages = computed(() => chatStore.currentMessages)
const isLoading = computed(() => chatStore.isLoading)
const isStreaming = computed(() => chatStore.isStreaming)
const streamingMessageId = computed(() => chatStore.streamingMessageId)

const inputPlaceholder = computed(() => {
  if (isLoading.value) return 'AI正在回复中...'
  if (currentMessages.value.length === 0) return '输入消息开始对话...'
  return '继续对话...'
})

// 快速建议
const quickSuggestions = [
  { icon: '💡', text: '解释一个概念' },
  { icon: '📝', text: '帮我写代码' },
  { icon: '🔍', text: '分析问题' },
  { icon: '🎨', text: '创意建议' },
  { icon: '📚', text: '学习计划' },
  { icon: '🚀', text: '项目规划' }
]

// 简化的方法
const sendSuggestion = (text: string) => {
  inputValue.value = text
  handleSend()
}

const handleSend = async () => {
  if (!inputValue.value.trim()) return

  const content = inputValue.value.trim()
  inputValue.value = ''

  try {
    console.log('开始发送消息:', content)

    // 如果没有当前对话，创建一个新对话
    if (!chatStore.currentConversation) {
      console.log('创建新对话...')
      await chatStore.createConversation('新对话')
      console.log('新对话已创建:', chatStore.currentConversation?.id)
    }

    // 发送消息
    console.log('发送消息到AI...')
    await chatStore.sendMessage(content)
    console.log('消息发送成功')
  } catch (error) {
    console.error('发送消息失败:', error)
    alert(`发送消息失败: ${error.message}`)
    // 发送失败时恢复输入内容
    inputValue.value = content
  }
}

// 滚动到底部处理
const handleScrollToBottom = () => {
  if (virtualMessageListRef.value) {
    virtualMessageListRef.value.scrollToBottom('smooth')
  }
  autoScroll.value = true
}

// 性能监控相关方法
const handleClearCache = () => {
  if (asyncRenderer.value?.clearCache) {
    asyncRenderer.value.clearCache()
  }
  console.log('缓存已清理')
}

const handleResetStats = () => {
  console.log('统计已重置')
}

const handleRegenerate = async (messageId: string) => {
  try {
    await chatStore.regenerateMessage(messageId)
  } catch (error) {
    console.error('重新生成消息失败:', error)
  }
}

// 加载更多消息
const loadMoreMessages = async () => {
  try {
    // 这里可以实现分页加载历史消息
    console.log('加载更多消息')
  } catch (error) {
    console.error('加载更多消息失败:', error)
  }
}

// 重复的函数已删除

const updatePerformanceSettings = () => {
  console.log('更新性能设置')
}


</script>

<style scoped>
.chat-view {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #ffffff;
  position: relative;
  overflow: hidden;
}

.chat-messages {
  flex: 1;
  overflow: hidden;
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.messages-container {
  height: 100%;
  overflow-y: auto;
  padding: 16px;
  scroll-behavior: smooth;
}

.message-wrapper {
  margin-bottom: 16px;
}

.loading-indicator,
.streaming-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 16px;
  color: #666;
  font-size: 14px;
}

.streaming-dots {
  display: flex;
  gap: 4px;
}

.streaming-dots span {
  width: 6px;
  height: 6px;
  background: #1890ff;
  border-radius: 50%;
  animation: streaming 1.4s infinite ease-in-out;
}

.streaming-dots span:nth-child(1) { animation-delay: -0.32s; }
.streaming-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes streaming {
  0%, 80%, 100% { transform: scale(0); }
  40% { transform: scale(1); }
}

.performance-panel {
  position: fixed;
  top: 20px;
  right: 20px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  min-width: 200px;
}

.performance-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  border-bottom: 1px solid #e5e7eb;
  font-weight: 600;
  font-size: 14px;
}

.performance-content {
  padding: 8px 12px;
}

.performance-item {
  padding: 4px 0;
  font-size: 12px;
  color: #666;
}

/* 欢迎消息样式 */
.welcome-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
  max-width: 600px;
  margin: 0 auto;
  padding: 20px;
}

.welcome-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.welcome-title {
  font-size: 22px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 12px;
}

.welcome-description {
  font-size: 14px;
  color: #6b7280;
  line-height: 1.5;
  margin-bottom: 24px;
}

.quick-suggestions {
  width: 100%;
  margin-bottom: 24px;
}

.quick-suggestions h3 {
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 12px;
}

.suggestion-buttons {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
  gap: 8px;
  max-width: 600px;
  margin: 0 auto;
}

.suggestion-btn {
  height: 36px;
  font-size: 13px;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.suggestion-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 性能设置 */
.performance-settings {
  margin-top: 16px;
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

/* 输入区域 */
.chat-input-area {
  border-top: 1px solid #e5e7eb;
  background: #ffffff;
  padding: 16px 20px;
  flex-shrink: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .welcome-message {
    padding: 16px;
  }

  .welcome-title {
    font-size: 20px;
  }

  .welcome-description {
    font-size: 13px;
  }

  .suggestion-buttons {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .suggestion-btn {
    height: 44px;
    font-size: 13px;
  }

  .chat-input-area {
    padding: 12px 16px;
  }

  .performance-settings {
    margin-top: 12px;
    padding: 12px;
  }
}

/* 简单输入框样式 */
.simple-input-container {
  display: flex;
  gap: 12px;
  align-items: flex-end;
}

.message-input {
  flex: 1;
}

.send-button {
  flex-shrink: 0;
  height: 40px;
}

/* 智能滚动样式 */
.messages-scrollbar {
  height: 100%;
}

.scroll-to-bottom {
  position: absolute;
  bottom: 80px;
  right: 20px;
  z-index: 10;
  opacity: 0.8;
  transition: opacity 0.3s;
}

.scroll-to-bottom:hover {
  opacity: 1;
}

/* 消息样式 */
.message-item {
  display: flex;
  margin-bottom: 24px;
  padding: 0 16px;
}

.message-avatar {
  flex-shrink: 0;
  margin-right: 12px;
}

.avatar-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: 500;
}

.avatar-icon.user {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.avatar-icon.assistant {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
}

.message-body {
  flex: 1;
  min-width: 0;
}

.message-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

.message-sender {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.message-timestamp {
  font-size: 12px;
  color: #9ca3af;
}

.message-content {
  background: #f9fafb;
  padding: 12px 16px;
  border-radius: 12px;
  border: 1px solid #f3f4f6;
  line-height: 1.6;
  color: #374151;
}

.user-message .message-content {
  background: #eff6ff;
  border-color: #dbeafe;
}

.message-actions {
  margin-top: 8px;
  display: flex;
  gap: 8px;
}

/* Markdown样式 */
.message-content h1.md-heading {
  font-size: 20px;
  font-weight: 700;
  color: #1f2937;
  margin: 20px 0 12px 0;
  border-bottom: 3px solid #3b82f6;
  padding-bottom: 6px;
}

.message-content h2.md-heading {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 16px 0 8px 0;
  border-bottom: 2px solid #e5e7eb;
  padding-bottom: 4px;
}

.message-content h3.md-heading {
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  margin: 12px 0 6px 0;
}

.message-content strong {
  font-weight: 600;
  color: #1f2937;
}

.message-content em {
  font-style: italic;
  color: #6b7280;
}

.message-content a {
  color: #3b82f6;
  text-decoration: none;
  border-bottom: 1px solid transparent;
  transition: all 0.2s;
}

.message-content a:hover {
  color: #2563eb;
  border-bottom-color: #3b82f6;
}

.message-content ul {
  margin: 12px 0;
  padding-left: 0;
  list-style: none;
}

.message-content li {
  position: relative;
  padding-left: 20px;
  margin-bottom: 4px;
}

.message-content li::before {
  content: '•';
  color: #3b82f6;
  font-weight: bold;
  position: absolute;
  left: 0;
}

.message-content .inline-code {
  background: #f3f4f6;
  color: #dc2626;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  border: 1px solid #e5e7eb;
}

.message-content .code-block {
  margin: 16px 0;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e5e7eb;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.message-content .code-header {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  padding: 4px 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #e5e7eb;
  min-height: 28px;
}

.message-content .code-lang {
  font-size: 11px;
  color: #6b7280;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.3px;
  line-height: 1;
}

.message-content .copy-code-btn {
  background: none;
  border: none;
  color: #6b7280;
  font-size: 11px;
  cursor: pointer;
  padding: 2px 6px;
  border-radius: 3px;
  transition: all 0.2s;
  font-weight: 500;
  line-height: 1;
  height: 20px;
  display: flex;
  align-items: center;
}

.message-content .copy-code-btn:hover {
  background: #e5e7eb;
  color: #374151;
}

.message-content pre {
  margin: 0;
  padding: 16px;
  background: #1e293b;
  color: #f1f5f9;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.5;
  overflow-x: auto;
}

.message-content code {
  font-family: inherit;
}

/* 专业消息样式 */
.message-item {
  display: flex;
  margin-bottom: 24px;
  padding: 0 16px;
}

.message-avatar {
  flex-shrink: 0;
  margin-right: 12px;
}

.avatar-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: 500;
}

.avatar-icon.user {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.avatar-icon.assistant {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
}

.message-body {
  flex: 1;
  min-width: 0;
}

.message-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

.message-sender {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.message-timestamp {
  font-size: 12px;
  color: #9ca3af;
}

.message-content {
  font-size: 14px;
  line-height: 1.6;
  color: #374151;
  word-wrap: break-word;
}

.message-content h2.message-heading {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 16px 0 8px 0;
  border-bottom: 2px solid #e5e7eb;
  padding-bottom: 4px;
}

.message-content h3.message-heading {
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  margin: 12px 0 6px 0;
}

.message-content .inline-code {
  background: #f3f4f6;
  color: #dc2626;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
}

.message-content .code-block {
  margin: 12px 0;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e5e7eb;
}

.code-header {
  background: #f8fafc;
  padding: 8px 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #e5e7eb;
}

.code-lang {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
}

.copy-code-btn {
  background: none;
  border: none;
  color: #6b7280;
  font-size: 12px;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.2s;
}

.copy-code-btn:hover {
  background: #e5e7eb;
  color: #374151;
}

.message-content pre {
  margin: 0;
  padding: 16px;
  background: #1f2937;
  color: #f9fafb;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.5;
  overflow-x: auto;
}

.message-actions {
  margin-top: 8px;
  display: flex;
  gap: 8px;
}

.user-message .message-content {
  background: #eff6ff;
  padding: 12px 16px;
  border-radius: 12px;
  border: 1px solid #dbeafe;
}

.assistant-message .message-content {
  background: #f9fafb;
  padding: 12px 16px;
  border-radius: 12px;
  border: 1px solid #f3f4f6;
}

/* Markdown内容样式 */
.markdown-content {
  line-height: 1.6;
  color: #374151;
}

.markdown-content p {
  margin: 0 0 12px 0;
}

.markdown-content p:last-child {
  margin-bottom: 0;
}

.markdown-content h1.message-heading {
  font-size: 20px;
  font-weight: 700;
  color: #1f2937;
  margin: 20px 0 12px 0;
  border-bottom: 3px solid #3b82f6;
  padding-bottom: 6px;
}

.markdown-content h2.message-heading {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 16px 0 8px 0;
  border-bottom: 2px solid #e5e7eb;
  padding-bottom: 4px;
}

.markdown-content h3.message-heading {
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  margin: 12px 0 6px 0;
}

.markdown-content strong {
  font-weight: 600;
  color: #1f2937;
}

.markdown-content em {
  font-style: italic;
  color: #6b7280;
}

.markdown-content a {
  color: #3b82f6;
  text-decoration: none;
  border-bottom: 1px solid transparent;
  transition: all 0.2s;
}

.markdown-content a:hover {
  color: #2563eb;
  border-bottom-color: #3b82f6;
}

.markdown-content ul {
  margin: 12px 0;
  padding-left: 0;
  list-style: none;
}

.markdown-content li {
  position: relative;
  padding-left: 20px;
  margin-bottom: 4px;
}

.markdown-content li::before {
  content: '•';
  color: #3b82f6;
  font-weight: bold;
  position: absolute;
  left: 0;
}

.markdown-content .inline-code {
  background: #f3f4f6;
  color: #dc2626;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  border: 1px solid #e5e7eb;
}

.markdown-content .code-block {
  margin: 16px 0;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e5e7eb;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.markdown-content .code-header {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  padding: 8px 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #e5e7eb;
}

.markdown-content .code-lang {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.markdown-content .copy-code-btn {
  background: none;
  border: none;
  color: #6b7280;
  font-size: 12px;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.2s;
  font-weight: 500;
}

.markdown-content .copy-code-btn:hover {
  background: #e5e7eb;
  color: #374151;
}

.markdown-content pre {
  margin: 0;
  padding: 16px;
  background: #1e293b;
  color: #f1f5f9;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.5;
  overflow-x: auto;
}

.markdown-content code {
  font-family: inherit;
}
</style>