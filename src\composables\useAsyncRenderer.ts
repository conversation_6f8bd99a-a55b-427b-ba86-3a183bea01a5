// 异步渲染组合式函数

import { ref, getCurrentInstance } from 'vue'
import { LRUCache } from '@/utils/performance'

interface RenderTask {
  resolve: (result: any) => void
  reject: (error: Error) => void
  timestamp: number
}

interface RenderOptions {
  language?: string
  enableCache?: boolean
  timeout?: number
}

export function useAsyncRenderer() {
  const worker = ref<Worker | null>(null)
  const pendingTasks = new Map<string, RenderTask>()
  const renderCache = new LRUCache<string, string>(100) // 缓存100个渲染结果
  const isWorkerReady = ref(false)

  // 初始化Worker
  const initWorker = () => {
    try {
      worker.value = new Worker('/workers/markdown.worker.js')
      
      worker.value.onmessage = (e) => {
        const { id, result, error, success, stats } = e.data
        const task = pendingTasks.get(id)

        if (task) {
          if (success) {
            // 缓存结果
            if (id.startsWith('markdown-') || id.startsWith('highlight-')) {
              const cacheKey = id.split('-').slice(1).join('-')
              renderCache.set(cacheKey, result)
            }

            // 记录性能统计
            if (stats) {
              // 性能统计已记录
            }

            task.resolve(result)
          } else {
            task.reject(new Error(error))
          }
          pendingTasks.delete(id)
        }
      }
      
      worker.value.onerror = (error) => {
        isWorkerReady.value = false
        
        // 清理所有待处理任务
        pendingTasks.forEach(task => {
          task.reject(new Error('Worker执行失败'))
        })
        pendingTasks.clear()
      }
      
      isWorkerReady.value = true
    } catch (error) {
      isWorkerReady.value = false
    }
  }

  // 渲染Markdown
  const renderMarkdown = async (
    content: string, 
    options: RenderOptions = {}
  ): Promise<string> => {
    const { enableCache = true, timeout = 5000 } = options
    
    // 检查缓存
    if (enableCache) {
      const cached = renderCache.get(content)
      if (cached) {
        return cached
      }
    }
    
    // 如果Worker不可用，使用降级方案
    if (!isWorkerReady.value || !worker.value) {
      return fallbackMarkdownRender(content)
    }
    
    return new Promise((resolve, reject) => {
      const id = `markdown-${crypto.randomUUID()}`
      const task: RenderTask = {
        resolve,
        reject,
        timestamp: Date.now()
      }
      
      pendingTasks.set(id, task)
      
      // 设置超时
      setTimeout(() => {
        if (pendingTasks.has(id)) {
          pendingTasks.delete(id)
          reject(new Error('渲染超时'))
        }
      }, timeout)
      
      worker.value!.postMessage({
        id,
        content,
        type: 'markdown',
        options
      })
    })
  }

  // 代码高亮
  const highlightCode = async (
    code: string,
    language: string,
    options: RenderOptions = {}
  ): Promise<string> => {
    const { enableCache = true, timeout = 3000 } = options
    const cacheKey = `${language}-${code}`
    
    // 检查缓存
    if (enableCache) {
      const cached = renderCache.get(cacheKey)
      if (cached) {
        return cached
      }
    }
    
    // 如果Worker不可用，使用降级方案
    if (!isWorkerReady.value || !worker.value) {
      return fallbackCodeHighlight(code, language)
    }
    
    return new Promise((resolve, reject) => {
      const id = `highlight-${crypto.randomUUID()}`
      const task: RenderTask = {
        resolve,
        reject,
        timestamp: Date.now()
      }
      
      pendingTasks.set(id, task)
      
      // 设置超时
      setTimeout(() => {
        if (pendingTasks.has(id)) {
          pendingTasks.delete(id)
          reject(new Error('高亮超时'))
        }
      }, timeout)
      
      worker.value!.postMessage({
        id,
        content: code,
        type: 'highlight',
        options: { language }
      })
    })
  }





  // 降级方案：高性能Markdown渲染
  const fallbackMarkdownRender = (content: string): string => {
    let html = content

    // 1. 处理代码块
    html = html.replace(/```(\w+)?\s*\n([\s\S]*?)\n```/g, (_, lang, code) => {
      const language = lang || ''
      const trimmedCode = code.trim()
      return `<div class="code-block">
        <div class="code-header">
          <span class="code-lang">${language.toUpperCase()}</span>
          <button class="copy-code-btn" data-code="${encodeURIComponent(trimmedCode)}">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
              <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
            </svg>
            <span class="copy-text">复制</span>
          </button>
        </div>
        <pre><code>${escapeHtml(trimmedCode)}</code></pre>
      </div>`
    })

    // 2. 处理标题
    html = html.replace(/^(#{1,6})\s+(.+)$/gm, (match, hashes, text) => {
      const level = hashes.length
      return `<h${level}>${text}</h${level}>`
    })

    // 3. 处理列表
    // 无序列表
    html = html.replace(/^\s*[-*+]\s+(.+)$/gm, '<li>$1</li>')
    // 有序列表
    html = html.replace(/^\s*\d+\.\s+(.+)$/gm, '<li>$1</li>')
    // 包装连续的li为ul
    html = html.replace(/(<li>[\s\S]*?<\/li>(?:\s*<li>[\s\S]*?<\/li>)*)/g, '<ul>$1</ul>')

    // 4. 处理引用
    html = html.replace(/^>\s+(.+)$/gm, '<blockquote>$1</blockquote>')

    // 5. 处理行内格式
    html = html
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      .replace(/`(.*?)`/g, '<code>$1</code>')
      .replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank">$1</a>')

    // 6. 处理换行
    html = html.replace(/\n/g, '<br>')

    return html
  }

  // HTML转义函数
  const escapeHtml = (text: string): string => {
    const div = document.createElement('div')
    div.textContent = text
    return div.innerHTML
  }

  // 降级方案：代码高亮
  const fallbackCodeHighlight = (code: string, language: string): string => {
    return `<pre><code class="language-${language}">${code}</code></pre>`
  }

  // 清理缓存
  const clearCache = () => {
    renderCache.clear()
  }

  // 获取缓存统计
  const getCacheStats = () => {
    return {
      size: renderCache.size,
      maxSize: 100
    }
  }

  // 获取待处理任务数量
  const getPendingTasksCount = () => {
    return pendingTasks.size
  }

  // 清理超时任务
  const cleanupTimeoutTasks = (timeout = 30000) => {
    const now = Date.now()
    const timeoutTasks: string[] = []
    
    pendingTasks.forEach((task, id) => {
      if (now - task.timestamp > timeout) {
        timeoutTasks.push(id)
      }
    })
    
    timeoutTasks.forEach(id => {
      const task = pendingTasks.get(id)
      if (task) {
        task.reject(new Error('任务超时'))
        pendingTasks.delete(id)
      }
    })
  }

  // 初始化
  initWorker()

  // 定期清理超时任务
  const cleanupInterval = setInterval(() => {
    cleanupTimeoutTasks()
  }, 10000)

  // 提供手动清理方法
  const cleanup = () => {
    if (worker.value) {
      worker.value.terminate()
      worker.value = null
    }

    pendingTasks.clear()
    clearInterval(cleanupInterval)
  }

  // 获取性能统计
  const getPerformanceStats = () => ({
    pendingTasks: pendingTasks.size,
    isWorkerReady: isWorkerReady.value,
    cacheStats: getCacheStats()
  })

  return {
    renderMarkdown,
    highlightCode,
    clearCache,
    getCacheStats,
    getPerformanceStats,
    getPendingTasksCount,
    isWorkerReady,
    cleanup
  }
}