<template>
  <div class="general-settings">
    <!-- 外观设置 -->
    <div class="settings-section">
      <div class="section-header">
        <h4>外观设置</h4>
        <p>自定义应用的外观和主题</p>
      </div>
      
      <div class="section-content">
        <div class="setting-item">
          <div class="setting-label">
            <label>主题模式</label>
            <small>选择应用的外观主题</small>
          </div>
          <div class="setting-control">
            <n-select
              v-model:value="appSettings.theme"
              :options="themeOptions"
              @update:value="updateAppSettings"
            />
          </div>
        </div>

        <div class="setting-item">
          <div class="setting-label">
            <label>字体大小</label>
            <small>调整界面文字大小</small>
          </div>
          <div class="setting-control">
            <n-slider
              v-model:value="appSettings.fontSize"
              :min="12"
              :max="20"
              :step="1"
              :marks="fontSizeMarks"
              @update:value="updateAppSettings"
            />
          </div>
        </div>

        <div class="setting-item">
          <div class="setting-label">
            <label>语言</label>
            <small>选择界面显示语言</small>
          </div>
          <div class="setting-control">
            <n-select
              v-model:value="appSettings.language"
              :options="languageOptions"
              @update:value="updateAppSettings"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 行为设置 -->
    <div class="settings-section">
      <div class="section-header">
        <h4>行为设置</h4>
        <p>配置应用的基本行为</p>
      </div>
      
      <div class="section-content">
        <div class="setting-item">
          <div class="setting-label">
            <label>自动滚动</label>
            <small>新消息时自动滚动到底部</small>
          </div>
          <div class="setting-control">
            <n-switch
              v-model:value="appSettings.autoScroll"
              @update:value="updateAppSettings"
            />
          </div>
        </div>

        <div class="setting-item">
          <div class="setting-label">
            <label>保存聊天历史</label>
            <small>自动保存对话记录</small>
          </div>
          <div class="setting-control">
            <n-switch
              v-model:value="appSettings.saveHistory"
              @update:value="updateAppSettings"
            />
          </div>
        </div>

        <div class="setting-item">
          <div class="setting-label">
            <label>显示行号</label>
            <small>在代码块中显示行号</small>
          </div>
          <div class="setting-control">
            <n-switch
              v-model:value="appSettings.showLineNumbers"
              @update:value="updateAppSettings"
            />
          </div>
        </div>

        <div class="setting-item">
          <div class="setting-label">
            <label>启用Markdown渲染</label>
            <small>渲染消息中的Markdown格式</small>
          </div>
          <div class="setting-control">
            <n-switch
              v-model:value="appSettings.enableMarkdown"
              @update:value="updateAppSettings"
            />
          </div>
        </div>

        <div class="setting-item">
          <div class="setting-label">
            <label>代码高亮</label>
            <small>启用代码语法高亮</small>
          </div>
          <div class="setting-control">
            <n-switch
              v-model:value="appSettings.enableCodeHighlight"
              @update:value="updateAppSettings"
            />
          </div>
        </div>

        <div class="setting-item">
          <div class="setting-label">
            <label>数学公式渲染</label>
            <small>渲染LaTeX数学公式</small>
          </div>
          <div class="setting-control">
            <n-switch
              v-model:value="appSettings.enableMath"
              @update:value="updateAppSettings"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 数据管理 -->
    <div class="settings-section">
      <div class="section-header">
        <h4>数据管理</h4>
        <p>管理应用数据和存储</p>
      </div>
      
      <div class="section-content">
        <div class="setting-item">
          <div class="setting-label">
            <label>历史记录保留天数</label>
            <small>超过此天数的记录将被自动清理</small>
          </div>
          <div class="setting-control">
            <n-input-number
              v-model:value="appSettings.maxHistoryDays"
              :min="1"
              :max="365"
              @update:value="updateAppSettings"
            />
          </div>
        </div>

        <div class="setting-item">
          <div class="setting-label">
            <label>清理数据</label>
            <small>清理缓存和临时文件</small>
          </div>
          <div class="setting-control">
            <n-button @click="clearCache" secondary>
              <template #icon>
                <i class="i-carbon-clean" />
              </template>
              清理缓存
            </n-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useMessage } from 'naive-ui'
import { useSettingsStore } from '@/stores/settings'

const message = useMessage()
const settingsStore = useSettingsStore()

// 计算属性
const appSettings = computed(() => settingsStore.app)

// 选项配置
const themeOptions = [
  { label: '跟随系统', value: 'auto' },
  { label: '浅色模式', value: 'light' },
  { label: '深色模式', value: 'dark' }
]

const languageOptions = [
  { label: '简体中文', value: 'zh-CN' },
  { label: 'English', value: 'en-US' },
  { label: '繁體中文', value: 'zh-TW' },
  { label: '日本語', value: 'ja-JP' }
]

const fontSizeMarks = {
  12: '小',
  14: '中',
  16: '大',
  18: '特大',
  20: '超大'
}

// 方法
const updateAppSettings = () => {
  settingsStore.saveSettings()
  message.success('设置已更新')
}

const clearCache = () => {
  // 实现清理缓存逻辑
  message.success('缓存已清理')
}
</script>

<style scoped>
.general-settings {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.settings-section {
  background: white;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  overflow: hidden;
}

.section-header {
  padding: 20px 24px;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

.section-header h4 {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 4px 0;
}

.section-header p {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
}

.section-content {
  padding: 24px;
}

.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 0;
  border-bottom: 1px solid #f1f5f9;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-label {
  flex: 1;
  margin-right: 24px;
}

.setting-label label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 4px;
}

.setting-label small {
  font-size: 12px;
  color: #9ca3af;
  line-height: 1.4;
}

.setting-control {
  flex: 0 0 200px;
  display: flex;
  justify-content: flex-end;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .setting-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .setting-label {
    margin-right: 0;
  }
  
  .setting-control {
    flex: none;
    width: 100%;
    justify-content: flex-start;
  }
}
</style>
