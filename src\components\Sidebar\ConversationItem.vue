<template>
  <div
    class="conversation-item"
    :class="{ active, collapsed }"
    @click="$emit('select', conversation.id)"
  >
    <div class="conversation-content">
      <div class="conversation-icon">
        <MessageSquare :size="16" />
      </div>
      
      <div v-if="!collapsed" class="conversation-info">
        <div class="conversation-title">
          {{ conversation.title }}
        </div>
        <div class="conversation-meta">
          <span class="message-count">{{ conversation.messageCount }} 条消息</span>
          <span class="update-time">{{ formatTime(conversation.updatedAt) }}</span>
        </div>
      </div>
      
      <div v-if="!collapsed" class="conversation-actions">
        <n-dropdown
          :options="menuOptions"
          @select="handleMenuAction"
          trigger="click"
        >
          <n-button text circle size="tiny" @click.stop>
            <template #icon>
              <MoreHorizontal :size="14" />
            </template>
          </n-button>
        </n-dropdown>
      </div>
    </div>
    
    <!-- 置顶标识 -->
    <div v-if="conversation.isPinned && !collapsed" class="pin-indicator">
      <Pin :size="12" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { MessageSquare, MoreHorizontal, Pin } from 'lucide-vue-next'
import { useChatStore } from '@/stores/chat'
import type { Conversation } from '@/types'

interface Props {
  conversation: Conversation
  active: boolean
  collapsed: boolean
  groupId?: string | null
}

const props = defineProps<Props>()

const emit = defineEmits<{
  select: [id: string]
  delete: [id: string]
  pin: [id: string]
  archive: [id: string]
  rename: [id: string, title: string]
  moveToGroup: [id: string, groupId?: string]
}>()

const chatStore = useChatStore()

// 移动到分组的选项
const moveToGroupOptions = computed(() => {
  const options = [
    {
      label: '未分组',
      key: 'move-to-ungrouped',
      icon: () => '📄'
    }
  ]

  // 添加所有分组
  chatStore.conversationGroups.forEach(group => {
    options.push({
      label: group.name,
      key: `move-to-${group.id}`,
      icon: () => group.icon || '📁'
    })
  })

  return options
})

// 菜单选项
const menuOptions = computed(() => [
  {
    label: props.conversation.isPinned ? '取消置顶' : '置顶',
    key: 'pin',
    icon: () => '📌'
  },
  {
    label: '重命名',
    key: 'rename',
    icon: () => '✏️'
  },
  {
    label: '移动',
    key: 'move',
    icon: () => '📁',
    children: moveToGroupOptions.value
  },
  {
    label: props.conversation.isArchived ? '取消归档' : '归档',
    key: 'archive',
    icon: () => '📦'
  },
  {
    type: 'divider',
    key: 'divider'
  },
  {
    label: '删除',
    key: 'delete',
    icon: () => '🗑️'
  }
])

// 格式化时间
const formatTime = (date: Date) => {
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  const minutes = Math.floor(diff / 60000)
  const hours = Math.floor(diff / 3600000)
  const days = Math.floor(diff / 86400000)
  
  if (minutes < 1) return '刚刚'
  if (minutes < 60) return `${minutes}分钟前`
  if (hours < 24) return `${hours}小时前`
  if (days < 7) return `${days}天前`
  
  return date.toLocaleDateString('zh-CN', {
    month: 'short',
    day: 'numeric'
  })
}

// 处理菜单操作
const handleMenuAction = (key: string) => {
  switch (key) {
    case 'pin':
      emit('pin', props.conversation.id)
      break
    case 'rename':
      const newTitle = prompt('请输入新标题', props.conversation.title)
      if (newTitle && newTitle.trim()) {
        emit('rename', props.conversation.id, newTitle.trim())
      }
      break
    case 'archive':
      emit('archive', props.conversation.id)
      break
    case 'delete':
      emit('delete', props.conversation.id)
      break
    default:
      // 处理移动到分组的操作
      if (key.startsWith('move-to-')) {
        const targetGroupId = key === 'move-to-ungrouped' ? undefined : key.replace('move-to-', '')
        emit('moveToGroup', props.conversation.id, targetGroupId)
      }
      break
  }
}


</script>

<style scoped>
.conversation-item {
  position: relative;
  padding: 12px 16px;
  cursor: pointer;
  transition: all 0.2s;
  border-radius: 8px;
  margin: 2px 8px;
}

.conversation-item:hover {
  background: #f3f4f6;
}

.conversation-item.active {
  background: #eff6ff;
  border-left: 3px solid #3b82f6;
}

.conversation-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.conversation-icon {
  flex-shrink: 0;
  color: #6b7280;
}

.conversation-info {
  flex: 1;
  min-width: 0;
}

.conversation-title {
  font-size: 14px;
  font-weight: 500;
  color: #111827;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.conversation-meta {
  display: flex;
  gap: 8px;
  font-size: 12px;
  color: #6b7280;
}

.message-count {
  opacity: 0.8;
}

.update-time {
  opacity: 0.6;
}

.conversation-actions {
  opacity: 0;
  transition: opacity 0.2s;
}

.conversation-item:hover .conversation-actions {
  opacity: 1;
}

.pin-indicator {
  position: absolute;
  top: 8px;
  right: 8px;
  color: #f59e0b;
}

.conversation-item.collapsed {
  padding: 8px;
  justify-content: center;
}

.conversation-item.collapsed .conversation-content {
  justify-content: center;
}

/* 暗色主题 */
.dark .conversation-item:hover {
  background: #374151;
}

.dark .conversation-item.active {
  background: #1e3a8a;
}

.dark .conversation-title {
  color: #f9fafb;
}
</style>