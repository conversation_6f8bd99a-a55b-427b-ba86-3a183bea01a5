<template>
  <div class="virtual-message-list" ref="containerRef">
    <n-scrollbar 
      ref="scrollbarRef" 
      class="message-scrollbar"
      @scroll="handleScroll"
    >
      <!-- 虚拟滚动容器 -->
      <div 
        class="virtual-container"
        :style="virtualScroll.getContainerStyle()"
      >
        <!-- 调试信息 -->
        <div v-if="messages.length === 0" style="padding: 20px; text-align: center; color: #666;">
          没有消息
        </div>


        <!-- 视口 -->
        <div
          class="virtual-viewport"
          :style="virtualScroll.getViewportStyle()"
        >
          <!-- 渲染可见消息 -->
          <div
            v-for="(message, index) in visibleMessages"
            :key="message.id"
            class="virtual-item"
            :style="virtualScroll.isEnabled ? virtualScroll.getItemStyle(index) : {}"
          >
            <MessageItem
              :message="message"
              :streaming="message.id === streamingMessageId"
              :streaming-message-id="streamingMessageId"
              @regenerate="handleRegenerate"
            />
          </div>
        </div>
      </div>
      
      <!-- 加载指示器 -->
      <div v-if="isLoading && !isStreaming" class="loading-indicator">
        <n-spin size="small" />
        <span>AI正在思考中...</span>
      </div>
    </n-scrollbar>
    
    <!-- 性能统计面板 -->
    <div v-if="showStats" class="performance-stats">
      <div class="stats-header">性能统计</div>
      <div class="stats-content">
        <div class="stat-item">
          <span>总消息: {{ messages?.length || 0 }}</span>
        </div>
        <div class="stat-item">
          <span>可见消息: {{ virtualScroll.visibleRange?.visibleItems?.length || 0 }}</span>
        </div>
        <div class="stat-item">
          <span>虚拟滚动: {{ virtualScroll.isEnabled ? '启用' : '禁用' }}</span>
        </div>
        <div class="stat-item">
          <span>内存使用: {{ memoryUsage }}MB</span>
        </div>
        <div class="stat-item" v-if="performanceMetrics">
          <span>内存占用: {{ performanceMetrics.memoryUsage }}%</span>
        </div>
        <div class="stat-item">
          <span>渲染缓存: {{ cacheStats.size }}/{{ cacheStats.maxSize }}</span>
        </div>
        <div class="stat-item" v-if="performanceMetrics">
          <span>虚拟化: {{ performanceMetrics.virtualScrollEnabled ? '是' : '否' }}</span>
        </div>
      </div>
    </div>
    
    <!-- 智能滚动到底部按钮 -->
    <transition name="fade">
      <n-button
        v-if="!isNearBottom && messages.length > 0"
        circle
        type="primary"
        class="scroll-to-bottom"
        @click="handleScrollToBottom"
      >
        <template #icon>
          <n-icon :component="ChevronDownIcon" />
        </template>
      </n-button>
    </transition>

    <!-- 智能滚动控制按钮 -->
    <n-button
      v-if="!isNearBottom && messages.length > 0"
      size="small"
      class="scroll-to-bottom-btn"
      @click="handleScrollToBottom"
      type="primary"
    >
      <template #icon>
        <n-icon :component="ArrowDownIcon" />
      </template>
      回到底部
    </n-button>

    <!-- 新消息提示 -->
    <transition name="fade">
      <div
        v-if="!isNearBottom && props.streamingMessageId"
        class="new-message-indicator"
      >
        <span>AI正在回复...</span>
        <n-button size="small" @click="handleScrollToBottom">
          查看最新回复
        </n-button>
      </div>
    </transition>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted, nextTick } from 'vue'
import { ChevronDown as ChevronDownIcon, ArrowDown as ArrowDownIcon } from 'lucide-vue-next'
import type { Message } from '@/types'
import { useVirtualScroll } from '@/composables/useVirtualScroll'
import { useAsyncRenderer } from '@/composables/useAsyncRenderer'
import { monitorMemoryUsage, PerformanceMonitor } from '@/utils/performance'
import MessageItem from './MessageItem.vue'

const props = withDefaults(defineProps<{
  messages: Message[]
  isLoading?: boolean
  isStreaming?: boolean
  streamingMessageId?: string | null
  showMetadata?: boolean
  showStats?: boolean
  itemHeight?: number
}>(), {
  messages: () => [],
  isLoading: false,
  isStreaming: false,
  streamingMessageId: null,
  showMetadata: false,
  showStats: false,
  itemHeight: 120
})

const emit = defineEmits<{
  regenerate: [messageId: string]
  loadMore: []
}>()

// 响应式数据
const containerRef = ref<HTMLElement>()
const scrollbarRef = ref()
const performanceMonitor = new PerformanceMonitor()
const asyncRenderer = useAsyncRenderer()

// 确保消息正确显示的计算属性
const visibleMessages = computed(() => {
  // 优先使用虚拟滚动的可见项
  const virtualItems = virtualScroll.visibleRange?.visibleItems
  if (virtualItems && virtualItems.length > 0) {
    return virtualItems
  }

  // 降级到直接使用所有消息
  return props.messages || []
})

// 虚拟滚动配置
const virtualScrollItems = computed(() =>
  (props.messages || []).map((msg, index) => ({
    id: msg.id,
    height: props.itemHeight,
    data: msg
  }))
)

const virtualScroll = useVirtualScroll(virtualScrollItems, {
  itemHeight: props.itemHeight,
  containerHeight: 600,
  overscan: 5, // 预渲染5个项目，提高滚动流畅度
  threshold: 50, // 超过50条消息启用虚拟滚动
  buffer: 10
})

// 性能监控
const memoryUsage = ref(0)
const performanceMetrics = ref<any>(null)

const updatePerformanceMetrics = () => {
  const memory = monitorMemoryUsage()
  if (memory) {
    memoryUsage.value = Math.round(memory.used / (1024 * 1024))
    performanceMetrics.value = {
      memoryUsage: memory.usagePercent,
      memoryAvailable: Math.round(memory.available / (1024 * 1024)),
      messagesCount: props.messages.length,
      virtualScrollEnabled: virtualScroll.isEnabled.value
    }
  }
}

// 缓存统计
const cacheStats = computed(() => asyncRenderer.getCacheStats())

// 简单的智能滚动状态
const isUserScrolling = ref(false)
const isAtBottom = ref(true)
const userHasScrolledUp = ref(false) // 用户是否向上滚动过
const isNearBottom = ref(true)
const userScrolling = ref(false)
const userManuallyScrolled = ref(false)
let scrollTimer: NodeJS.Timeout | null = null
let scrollTimeout: NodeJS.Timeout | null = null
let positionCheckInterval: NodeJS.Timeout | null = null

// 滚动处理
const handleScroll = (e: Event) => {
  virtualScroll.handleScroll(e)

  // 标记用户正在滚动
  userScrolling.value = true
  userManuallyScrolled.value = true


  // 清除之前的定时器
  if (scrollTimeout) {
    clearTimeout(scrollTimeout)
  }

  // 检查是否接近底部 - 尝试多种方式获取滚动信息
  let scrollTop = 0, scrollHeight = 0, clientHeight = 0
  let detectionMethod = 'none'

  // 方法1: 直接从事件目标获取
  const target = e.target as HTMLElement
  if (target && target.scrollTop !== undefined && target.scrollHeight > 0) {
    scrollTop = target.scrollTop
    scrollHeight = target.scrollHeight
    clientHeight = target.clientHeight
    detectionMethod = 'event-target'
  }

  // 方法2: 从scrollbarRef获取
  if (scrollHeight === 0 && scrollbarRef.value) {
    const scrollContainer = scrollbarRef.value.$el?.querySelector('.n-scrollbar-container')
    if (scrollContainer) {
      scrollTop = scrollContainer.scrollTop
      scrollHeight = scrollContainer.scrollHeight
      clientHeight = scrollContainer.clientHeight
      detectionMethod = 'n-scrollbar-container'
    }
  }

  // 方法3: 从containerRef获取
  if (scrollHeight === 0 && containerRef.value) {
    scrollTop = containerRef.value.scrollTop
    scrollHeight = containerRef.value.scrollHeight
    clientHeight = containerRef.value.clientHeight
    detectionMethod = 'container-ref'
  }

  // 计算是否接近底部
  if (scrollHeight > 0) {
    const threshold = 50 // 减少阈值到50px，更精确
    const wasNearBottom = isNearBottom.value
    const isAtBottom = scrollTop + clientHeight >= scrollHeight - threshold
    const distanceFromBottom = scrollHeight - (scrollTop + clientHeight)

    isNearBottom.value = isAtBottom

    console.log(`📊 滚动检测 [${detectionMethod}]:`, {
      scrollTop: Math.round(scrollTop),
      scrollHeight: Math.round(scrollHeight),
      clientHeight: Math.round(clientHeight),
      distanceFromBottom: Math.round(distanceFromBottom),
      isAtBottom,
      userManuallyScrolled: userManuallyScrolled.value
    })

    // 如果用户真的滚动到了底部，重新启用自动滚动
    if (isAtBottom && userManuallyScrolled.value) {
      userManuallyScrolled.value = false
      console.log('✅ 用户滚动到底部，重新启用自动滚动')
    }

    // 状态变化时的调试信息
    if (wasNearBottom !== isNearBottom.value) {
      console.log('📍 底部状态变化:', isNearBottom.value ? '接近底部' : '远离底部')
    }
  } else {
    console.warn('⚠️ 无法获取滚动信息，检测方法:', detectionMethod)
  }

  // 这个逻辑已经在上面的底部检测中处理了

  // 延长用户滚动状态，确保不被打断
  scrollTimeout = setTimeout(() => {
    userScrolling.value = false

    // 用户停止滚动后，立即检查位置
    if (userManuallyScrolled.value) {
      console.log('⏰ 用户停止滚动，检查是否在底部')
      const position = checkScrollPosition()
      if (position) {
        console.log('📊 停止滚动后的位置:', position)
      }
    }
  }, 1000) // 增加到1秒

  // 检查是否需要加载更多
  if (virtualScroll.isNearBottom.value && !props.isLoading) {
    emit('loadMore')
  }
}

// 滚动到底部 - 修复滚动问题
const scrollToBottom = (behavior: ScrollBehavior = 'smooth') => {
  nextTick(() => {
    try {
      // 方法1: 尝试NScrollbar的scrollTo方法
      if (scrollbarRef.value && scrollbarRef.value.scrollTo) {
        scrollbarRef.value.scrollTo({ top: 999999, behavior })
        return
      }

      // 方法2: 尝试NScrollbar的内部容器
      if (scrollbarRef.value && scrollbarRef.value.$el) {
        const scrollContainer = scrollbarRef.value.$el.querySelector('.n-scrollbar-container')
        if (scrollContainer) {
          scrollContainer.scrollTop = scrollContainer.scrollHeight
          return
        }
      }

      // 方法3: 降级到外部容器
      if (containerRef.value) {
        containerRef.value.scrollTop = containerRef.value.scrollHeight
        return
      }
    } catch (error) {
      console.error('滚动失败:', error)
    }
  })
}

// 强制滚动到底部 - 直接操作DOM
const forceScrollToBottom = () => {
  try {
    // 查找所有可能的滚动容器
    const containers = [
      containerRef.value,
      scrollbarRef.value?.$el,
      document.querySelector('.n-scrollbar-container'),
      document.querySelector('.message-scrollbar'),
      document.querySelector('.virtual-message-list')
    ].filter(Boolean)

    containers.forEach((container, index) => {
      if (container && container.scrollHeight) {
        container.scrollTop = container.scrollHeight
      }
    })
  } catch (error) {
    console.error('强制滚动失败:', error)
  }
}

// 滚动到最后一个消息元素
const scrollToLastMessage = () => {
  try {
    // 查找最后一个消息元素
    const messageElements = document.querySelectorAll('.virtual-item')
    if (messageElements.length > 0) {
      const lastElement = messageElements[messageElements.length - 1]
      lastElement.scrollIntoView({
        behavior: 'auto',
        block: 'end',
        inline: 'nearest'
      })

    }
  } catch (error) {
    console.error('滚动到最后消息失败:', error)
  }
}

// 处理用户点击滚动到底部
const handleScrollToBottom = () => {
  // 用户主动点击，重置所有状态并滚动
  userScrolling.value = false
  userManuallyScrolled.value = false // 重置手动滚动标记
  isNearBottom.value = true
  console.log('✅ 用户点击回到底部，重新启用自动滚动')
  scrollToBottom('smooth')

  // 确保滚动到位
  setTimeout(() => {
    scrollToLastMessage()
  }, 100)
}

// 检查滚动位置（独立函数，用于定期检查）
const checkScrollPosition = () => {
  let scrollTop = 0, scrollHeight = 0, clientHeight = 0
  let detectionMethod = 'none'

  // 尝试多种方式获取滚动信息
  if (scrollbarRef.value) {
    // 尝试获取 Naive UI scrollbar 的内部容器
    const scrollbarEl = scrollbarRef.value.$el || scrollbarRef.value
    if (scrollbarEl && scrollbarEl.querySelector) {
      const scrollContainer = scrollbarEl.querySelector('.n-scrollbar-container')
      if (scrollContainer) {
        scrollTop = scrollContainer.scrollTop
        scrollHeight = scrollContainer.scrollHeight
        clientHeight = scrollContainer.clientHeight
        detectionMethod = 'n-scrollbar-container'
      }
    }
  }

  if (scrollHeight === 0 && containerRef.value) {
    scrollTop = containerRef.value.scrollTop
    scrollHeight = containerRef.value.scrollHeight
    clientHeight = containerRef.value.clientHeight
    detectionMethod = 'container-ref'
  }

  // 计算是否在底部
  if (scrollHeight > 0) {
    const threshold = 10 // 减小阈值，更严格的底部检测
    const isAtBottom = scrollTop + clientHeight >= scrollHeight - threshold
    const distanceFromBottom = scrollHeight - (scrollTop + clientHeight)

    // 只更新isNearBottom状态，不在这里修改userManuallyScrolled
    isNearBottom.value = isAtBottom

    return {
      scrollTop: Math.round(scrollTop),
      scrollHeight: Math.round(scrollHeight),
      clientHeight: Math.round(clientHeight),
      distanceFromBottom: Math.round(distanceFromBottom),
      isAtBottom,
      detectionMethod
    }
  }

  return null
}

// 调试滚动状态
const debugScrollState = () => {
  const position = checkScrollPosition()
  console.log('🐛 调试滚动状态:', {
    position,
    isNearBottom: isNearBottom.value,
    userScrolling: userScrolling.value,
    userManuallyScrolled: userManuallyScrolled.value,
    isUserScrolling: isUserScrolling.value,
    userHasScrolledUp: userHasScrolledUp.value,
    messagesCount: props.messages.length,
    virtualScrollEnabled: virtualScroll.isEnabled.value
  })
}

// 重复的方法已删除，使用上面的handleScrollToBottom

// 滚动到指定消息
const scrollToMessage = (messageId: string) => {
  const index = props.messages.findIndex(m => m.id === messageId)
  if (index !== -1) {
    virtualScroll.scrollToItem(index)
  }
}

// 处理重新生成
const handleRegenerate = (messageId: string) => {
  emit('regenerate', messageId)
}

// 性能优化：防抖滚动处理
let performanceUpdateTimeout: NodeJS.Timeout | null = null
const debouncedScrollUpdate = () => {
  if (performanceUpdateTimeout) {
    clearTimeout(performanceUpdateTimeout)
  }

  performanceUpdateTimeout = setTimeout(() => {
    updatePerformanceMetrics()
  }, 1000)
}

// 监听消息变化
watch(
  () => props.messages.length,
  (newLength, oldLength) => {
    // 新消息添加时的真正智能滚动逻辑
    if (newLength > oldLength) {
      // 只有在用户没有手动滚动离开底部时才自动滚动
      if (!userManuallyScrolled.value) {
        console.log('📨 新消息到达，自动滚动到底部')
        nextTick(() => {
          scrollToBottom()
        })
      } else {
        console.log('📨 新消息到达，但用户已手动滚动，不自动滚动（需要用户主动回到底部）')
      }
    }

    // 重新计算虚拟滚动
    virtualScroll.recalculate()
  }
)

// 监听流式消息 - 修复滚动跟随
watch(
  () => props.streamingMessageId,
  (newId, oldId) => {
    if (newId && newId !== oldId) {
      // 新的流式消息开始时，只有在用户没有手动滚动时才自动滚动
      if (!userManuallyScrolled.value) {
        console.log('🔄 开始流式消息，自动滚动到底部')
        nextTick(() => {
          scrollToBottom()
        })
      } else {
        console.log('🔄 开始流式消息，但用户已手动滚动，不自动滚动')
      }
    }
  }
)

// 监听流式消息内容变化 - 智能跟随滚动
let streamingScrollTimer: NodeJS.Timeout | null = null
watch(
  () => {
    // 找到流式消息的内容
    const streamingMessage = props.messages.find(m => m.id === props.streamingMessageId)
    return streamingMessage?.content || ''
  },
  (newContent, oldContent) => {
    if (newContent && newContent !== oldContent && props.streamingMessageId) {
      // 清除之前的定时器
      if (streamingScrollTimer) {
        clearTimeout(streamingScrollTimer)
      }

      // 智能滚动：只有在用户没有手动滚动离开底部时才自动滚动
      streamingScrollTimer = setTimeout(() => {
        // 真正的智能流式滚动：只有用户没有手动滚动时才跟随
        if (!userManuallyScrolled.value && !userScrolling.value) {
          scrollToBottom('auto')

          // 轻量级的确保滚动
          setTimeout(() => {
            if (!userScrolling.value && !userManuallyScrolled.value) {
              scrollToLastMessage()
            }
          }, 10)
        }
      }, 50)
    }
  }
)

// 监听容器大小变化
const resizeObserver = ref<ResizeObserver | null>(null)

onMounted(() => {
  // 初始化性能监控
  updatePerformanceMetrics()

  // 设置定时器更新性能指标
  const memoryInterval = setInterval(updatePerformanceMetrics, 5000)

  // 监听容器大小变化
  if (containerRef.value && 'ResizeObserver' in window) {
    resizeObserver.value = new ResizeObserver((entries) => {
      for (const entry of entries) {
        const { height } = entry.contentRect
        virtualScroll.updateConfig({ containerHeight: height })
      }
    })

    resizeObserver.value.observe(containerRef.value)
  }

  // 监听鼠标滚轮事件，更强的用户滚动检测
  let wheelHandler: ((e: WheelEvent) => void) | null = null

  wheelHandler = (event: WheelEvent) => {
    userScrolling.value = true

    // 判断滚动方向
    const isScrollingDown = event.deltaY > 0

    if (isScrollingDown) {
      // 向下滚动时，检查是否到底部
      checkIfAtBottom()
    } else {
      // 向上滚动时，标记为手动滚动
      userManuallyScrolled.value = true
      console.log('🖱️ 用户向上滚动，禁用自动滚动')
    }

    // 清除之前的定时器
    if (scrollTimeout) {
      clearTimeout(scrollTimeout)
    }

    // 延长用户滚动状态
    scrollTimeout = setTimeout(() => {
      userScrolling.value = false
    }, 1500) // 滚轮事件后更长的保护时间
  }

  // 添加滚轮事件监听
  if (containerRef.value) {
    containerRef.value.addEventListener('wheel', wheelHandler, { passive: true })
  }

  // 直接监听滚轮事件后检查位置
  const checkIfAtBottom = () => {
    if (userManuallyScrolled.value) {
      // 延迟检查，等待滚动完成
      setTimeout(() => {
        const position = checkScrollPosition()
        if (position && position.distanceFromBottom <= 30) {
          userManuallyScrolled.value = false
          isNearBottom.value = true
          console.log('✅ 智能滚动：用户滚动到底部，重新启用自动滚动')
        }
      }, 500) // 等待500ms让滚动完成
    }
  }

  // 不需要启动函数了

  // 暂时禁用定期检查，避免自动重新启用滚动
  // positionCheckInterval = setInterval(() => {
  //   // 定期检查逻辑已禁用
  // }, 1000)

  // 初始滚动到底部
  nextTick(() => {
    scrollToBottom()
  })
  
  // 清理函数
  onUnmounted(() => {
    clearInterval(memoryInterval)

    if (resizeObserver.value) {
      resizeObserver.value.disconnect()
    }

    // 移除滚轮事件监听
    if (containerRef.value && wheelHandler) {
      containerRef.value.removeEventListener('wheel', wheelHandler)
    }

    // 不需要清理观察器了

    if (scrollTimeout) {
      clearTimeout(scrollTimeout)
    }

    if (performanceUpdateTimeout) {
      clearTimeout(performanceUpdateTimeout)
    }

    if (streamingScrollTimer) {
      clearTimeout(streamingScrollTimer)
    }

    if (positionCheckInterval) {
      clearInterval(positionCheckInterval)
    }
  })
})

// 暴露方法给父组件
defineExpose({
  scrollToBottom,
  scrollToMessage,
  getStats: () => ({
    virtualScroll: virtualScroll.getStats(),
    memory: memoryUsage.value,
    cache: cacheStats.value,
    performance: performanceMonitor.getMetrics()
  })
})
</script>

<style scoped>
.virtual-message-list {
  height: 100%;
  position: relative;
  /* 移除 overflow: hidden，让NScrollbar处理滚动 */
}

.message-scrollbar {
  height: 100%;
}

.virtual-container {
  width: 100%;
}

.virtual-viewport {
  position: relative;
  width: 100%;
}

.virtual-item {
  padding: 0 20px;
}

/* 加载指示器 */
.loading-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 20px;
  color: #6b7280;
  font-size: 14px;
}

/* 性能统计面板 */
.performance-stats {
  position: absolute;
  top: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 12px;
  border-radius: 8px;
  font-size: 12px;
  min-width: 200px;
  z-index: 100;
}

.stats-header {
  font-weight: 600;
  margin-bottom: 8px;
  text-align: center;
}

.stats-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 滚动到底部按钮 */
.scroll-to-bottom {
  position: absolute;
  bottom: 20px;
  right: 20px;
  width: 40px;
  height: 40px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 10;
}

/* 新消息提示 */
.new-message-indicator {
  position: absolute;
  bottom: 70px;
  right: 20px;
  z-index: 10;
  background: rgba(24, 144, 255, 0.9);
  color: white;
  padding: 8px 12px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(8px);
}

.new-message-indicator span {
  white-space: nowrap;
}

/* 过渡动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .virtual-item {
    padding: 0 16px;
  }
  
  .performance-stats {
    top: 5px;
    right: 5px;
    padding: 8px;
    min-width: 160px;
    font-size: 11px;
  }
  
  .scroll-to-bottom {
    bottom: 15px;
    right: 15px;
    width: 36px;
    height: 36px;
  }
}

/* 性能优化 */
.virtual-container {
  contain: layout style paint;
}

.virtual-viewport {
  contain: layout style paint;
  will-change: transform;
}

.virtual-item {
  contain: layout style paint;
}

/* 回到底部按钮样式 */
.scroll-to-bottom-btn {
  position: absolute;
  bottom: 20px;
  right: 20px;
  z-index: 10;
  border-radius: 50%;
  width: 48px;
  height: 48px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.scroll-to-bottom-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
}
</style>