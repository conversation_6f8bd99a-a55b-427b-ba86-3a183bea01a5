{"name": "ai-desktop-client", "private": true, "version": "1.0.0", "type": "module", "description": "高性能AI桌面客户端 - 基于Tauri + Vue3", "author": "Your Name", "license": "MIT", "scripts": {"dev": "vite --port 1420", "build": "vue-tsc --noEmit && vite build", "preview": "vite preview", "start": "vite --open", "tauri": "tauri", "tauri:dev": "cargo tauri dev", "tauri:build": "cargo tauri build", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/", "test:performance": "echo '启动性能测试模式' && vite --port 1420 --mode performance", "analyze": "vite build --mode analyze", "type-check": "vue-tsc --noEmit", "clean": "rimraf dist && rimraf src-tauri/target"}, "dependencies": {"@tauri-apps/api": "^2.0.0", "@vueuse/core": "^10.7.0", "katex": "^0.16.9", "lucide-vue-next": "^0.294.0", "marked": "^11.1.1", "marked-highlight": "^2.0.6", "naive-ui": "^2.38.1", "pinia": "^2.1.7", "prismjs": "^1.29.0", "vue": "^3.4.0", "vue-router": "^4.2.5"}, "devDependencies": {"@iconify/json": "^2.2.170", "@iconify/vue": "^4.1.1", "@tauri-apps/cli": "^1.5.8", "@types/katex": "^0.16.7", "@types/marked": "^6.0.0", "@types/node": "^20.10.6", "@types/prismjs": "^1.26.3", "@typescript-eslint/eslint-plugin": "^6.16.0", "@typescript-eslint/parser": "^6.16.0", "@unocss/preset-icons": "^0.58.0", "@unocss/preset-uno": "^0.58.0", "@vitejs/plugin-vue": "^4.5.2", "eslint": "^8.56.0", "eslint-plugin-vue": "^9.19.2", "prettier": "^3.1.1", "typescript": "^5.8.3", "unocss": "^0.58.0", "vite": "^5.0.10", "vue-tsc": "^3.0.4"}, "engines": {"node": ">=18.0.0"}}