// 虚拟滚动组合式函数

import { ref, computed, onMounted, onUnmounted, nextTick, unref, type Ref } from 'vue'
import { throttle } from '@/utils/performance'

export interface VirtualScrollConfig {
  itemHeight: number        // 每项高度
  containerHeight: number   // 容器高度
  overscan: number         // 预渲染项目数
  threshold: number        // 启用虚拟滚动的阈值
  buffer: number           // 缓冲区大小
}

export interface VirtualScrollItem {
  id: string | number
  height?: number
  data: any
}

const DEFAULT_CONFIG: VirtualScrollConfig = {
  itemHeight: 100,
  containerHeight: 600,
  overscan: 3,
  threshold: 50,
  buffer: 10
}

export function useVirtualScroll<T extends VirtualScrollItem>(
  items: T[] | Ref<T[]>,
  config: Partial<VirtualScrollConfig> = {}
) {
  const finalConfig = { ...DEFAULT_CONFIG, ...config }
  
  const scrollTop = ref(0)
  const containerRef = ref<HTMLElement>()
  const isEnabled = ref(false)
  
  // 计算可见范围
  const visibleRange = computed(() => {
    const itemsArray = unref(items)

    // 当虚拟滚动未启用时，返回所有项目
    if (!isEnabled.value || itemsArray.length < finalConfig.threshold) {
      return {
        start: 0,
        end: Math.max(0, itemsArray.length - 1),
        visibleItems: itemsArray
      }
    }
    
    const startIndex = Math.floor(scrollTop.value / finalConfig.itemHeight)
    const endIndex = Math.min(
      startIndex + Math.ceil(finalConfig.containerHeight / finalConfig.itemHeight),
      itemsArray.length - 1
    )
    
    const start = Math.max(0, startIndex - finalConfig.overscan)
    const end = Math.min(itemsArray.length - 1, endIndex + finalConfig.overscan)
    
    return {
      start,
      end,
      visibleItems: itemsArray.slice(start, end + 1)
    }
  })
  
  // 计算总高度
  const totalHeight = computed(() => {
    const itemsArray = unref(items)
    if (!isEnabled.value) return 'auto'
    return itemsArray.length * finalConfig.itemHeight
  })
  
  // 计算偏移量
  const offsetY = computed(() => {
    if (!isEnabled.value) return 0
    return visibleRange.value.start * finalConfig.itemHeight
  })
  
  // 滚动处理函数
  const handleScroll = throttle((event: Event) => {
    const target = event.target as HTMLElement
    scrollTop.value = target.scrollTop
  }, 16) // 60fps
  
  // 滚动到指定项
  const scrollToItem = (index: number, behavior: ScrollBehavior = 'smooth') => {
    if (!containerRef.value) return
    
    const targetScrollTop = index * finalConfig.itemHeight
    containerRef.value.scrollTo({
      top: targetScrollTop,
      behavior
    })
  }
  
  // 滚动到底部
  const scrollToBottom = (behavior: ScrollBehavior = 'smooth') => {
    if (!containerRef.value) return
    
    containerRef.value.scrollTo({
      top: containerRef.value.scrollHeight,
      behavior
    })
  }
  
  // 滚动到顶部
  const scrollToTop = (behavior: ScrollBehavior = 'smooth') => {
    if (!containerRef.value) return
    
    containerRef.value.scrollTo({
      top: 0,
      behavior
    })
  }
  
  // 检查是否需要启用虚拟滚动
  const checkShouldEnable = () => {
    const itemsArray = unref(items)
    isEnabled.value = itemsArray.length >= finalConfig.threshold
  }
  
  // 获取项目样式
  const getItemStyle = (index: number) => {
    if (!isEnabled.value) return {}
    
    const actualIndex = visibleRange.value.start + index
    return {
      position: 'absolute' as const,
      top: `${actualIndex * finalConfig.itemHeight}px`,
      left: 0,
      right: 0,
      height: `${finalConfig.itemHeight}px`
    }
  }
  
  // 获取容器样式
  const getContainerStyle = () => {
    if (!isEnabled.value) return {}
    
    return {
      position: 'relative' as const,
      height: typeof totalHeight.value === 'number' ? `${totalHeight.value}px` : totalHeight.value,
      overflow: 'hidden'
    }
  }
  
  // 获取视口样式
  const getViewportStyle = () => {
    if (!isEnabled.value) return {}
    
    return {
      transform: `translateY(${offsetY.value}px)`
    }
  }
  
  // 检查是否接近底部
  const isNearBottom = computed(() => {
    if (!containerRef.value) return false
    
    const { scrollTop: currentScrollTop, scrollHeight, clientHeight } = containerRef.value
    return currentScrollTop + clientHeight >= scrollHeight - 100
  })
  
  // 检查是否在顶部
  const isAtTop = computed(() => {
    return scrollTop.value <= 10
  })
  
  // 更新配置
  const updateConfig = (newConfig: Partial<VirtualScrollConfig>) => {
    Object.assign(finalConfig, newConfig)
    checkShouldEnable()
  }
  
  // 重新计算
  const recalculate = () => {
    checkShouldEnable()
    nextTick(() => {
      if (containerRef.value) {
        scrollTop.value = containerRef.value.scrollTop
      }
    })
  }
  
  // 获取统计信息
  const getStats = () => {
    const itemsArray = unref(items)
    return {
      totalItems: itemsArray.length,
      visibleItems: visibleRange.value.visibleItems.length,
      isEnabled: isEnabled.value,
      scrollTop: scrollTop.value,
      visibleRange: {
        start: visibleRange.value.start,
        end: visibleRange.value.end
      }
    }
  }
  
  // 生命周期
  onMounted(() => {
    checkShouldEnable()
    
    if (containerRef.value) {
      containerRef.value.addEventListener('scroll', handleScroll, { passive: true })
    }
  })
  
  onUnmounted(() => {
    if (containerRef.value) {
      containerRef.value.removeEventListener('scroll', handleScroll)
    }
  })
  
  return {
    // 响应式数据
    containerRef,
    isEnabled,
    visibleRange,
    totalHeight,
    offsetY,
    scrollTop,
    isNearBottom,
    isAtTop,
    
    // 方法
    scrollToItem,
    scrollToBottom,
    scrollToTop,
    getItemStyle,
    getContainerStyle,
    getViewportStyle,
    updateConfig,
    recalculate,
    getStats,
    
    // 事件处理
    handleScroll
  }
}