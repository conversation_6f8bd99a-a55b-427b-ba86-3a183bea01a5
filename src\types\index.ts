// 基础类型定义

export interface Message {
  id: string
  conversationId: string
  role: 'user' | 'assistant' | 'system'
  content: string
  timestamp: Date
  streaming?: boolean
  tokens?: number
  model?: string
  // 思考模型支持
  reasoning?: string // 思考过程内容
  isReasoningModel?: boolean // 是否为思考模型
  reasoningComplete?: boolean // 思考过程是否完成
}

export interface ConversationGroup {
  id: string
  name: string
  description?: string
  color?: string
  icon?: string
  createdAt: Date
  updatedAt: Date
  conversationCount: number
  isCollapsed?: boolean
}

export interface Conversation {
  id: string
  title: string
  createdAt: Date
  updatedAt: Date
  messageCount: number
  model?: string
  isArchived?: boolean
  isPinned?: boolean
  groupId?: string // 所属分组ID
  tags?: string[] // 标签
  chatSettings?: ChatSettings // 会话级别的模型参数设置
}

export interface AIProvider {
  id: string
  name: string
  displayName: string
  apiKey?: string
  baseUrl?: string
  models: AIModel[]
  selectedModels?: string[] // 用户选中的模型ID列表
  favoriteModels?: string[] // 用户收藏的模型ID列表
  enabled: boolean
  icon?: string
}

export interface AIModel {
  id: string
  name: string
  displayName: string
  contextLength: number
  inputPrice?: number
  outputPrice?: number
  supportStreaming: boolean
  supportVision?: boolean
  supportFunction?: boolean
  isReasoningModel?: boolean // 是否为思考模型
}

export interface ChatSettings {
  temperature: number
  maxTokens: number
  topP: number
  frequencyPenalty: number
  presencePenalty: number
  systemPrompt?: string
}

export interface AppSettings {
  theme: 'light' | 'dark' | 'auto'
  language: string
  fontSize: number
  autoScroll: boolean
  showLineNumbers: boolean
  enableMarkdown: boolean
  enableCodeHighlight: boolean
  enableMath: boolean
  saveHistory: boolean
  maxHistoryDays: number
}

export interface FileAttachment {
  id: string
  name: string
  type: string
  size: number
  url?: string
  content?: string
  thumbnail?: string
}

export interface Assistant {
  id: string
  name: string
  description: string
  avatar?: string
  systemPrompt: string
  model?: string
  settings?: Partial<ChatSettings>
  isBuiltIn: boolean
  isEnabled: boolean
  category: string
  tags: string[]
  createdAt: Date
  updatedAt: Date
}

export interface SearchResult {
  id: string
  type: 'message' | 'conversation' | 'assistant'
  title: string
  content: string
  conversationId?: string
  messageId?: string
  timestamp: Date
  relevance: number
}

// API响应类型
export interface APIResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  code?: number
}

export interface StreamingResponse {
  id: string
  content: string
  isComplete: boolean
  error?: string
  // 思考模型支持
  reasoning?: string // 思考过程内容
  reasoningComplete?: boolean // 思考过程是否完成
  model?: string
  tokens?: number
}

// 事件类型
export interface AppEvent {
  type: string
  payload: any
  timestamp: Date
}

// 组件Props类型
export interface MessageItemProps {
  message: Message
  showAvatar?: boolean
  showTimestamp?: boolean
  enableActions?: boolean
}

export interface ConversationItemProps {
  conversation: Conversation
  isActive?: boolean
  showPreview?: boolean
}

export interface ChatInputProps {
  placeholder?: string
  maxLength?: number
  disabled?: boolean
  multiline?: boolean
  showAttachments?: boolean
}

// 状态类型
export interface ChatState {
  conversations: Conversation[]
  conversationGroups: ConversationGroup[]
  currentConversation: Conversation | null
  currentGroup: ConversationGroup | null
  messages: Message[]
  isLoading: boolean
  isStreaming: boolean
  streamingMessageId: string | null
}

export interface SettingsState {
  app: AppSettings
  chat: ChatSettings
  providers: AIProvider[]
  assistants: Assistant[]
}

export interface UIState {
  sidebarCollapsed: boolean
  currentView: string
  searchQuery: string
  searchResults: SearchResult[]
  notifications: Notification[]
}

export interface Notification {
  id: string
  type: 'info' | 'success' | 'warning' | 'error'
  title: string
  message: string
  duration?: number
  timestamp: Date
}
