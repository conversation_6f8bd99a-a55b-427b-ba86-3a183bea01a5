<template>
  <div class="settings-view">
    <div class="settings-container">
      <div class="settings-header">
        <h1>设置</h1>
        <p>配置您的AI桌面客户端</p>
      </div>

      <n-tabs type="line" animated>
        <!-- 通用设置 -->
        <n-tab-pane name="general" tab="通用">
          <div class="settings-section">
            <h3>外观</h3>
            <div class="setting-item">
              <label>主题</label>
              <n-select
                v-model:value="appSettings.theme"
                :options="themeOptions"
                @update:value="updateAppSettings"
              />
            </div>
            
            <div class="setting-item">
              <label>字体大小</label>
              <n-slider
                v-model:value="appSettings.fontSize"
                :min="12"
                :max="20"
                :step="1"
                :marks="fontSizeMarks"
                @update:value="updateAppSettings"
              />
            </div>
          </div>

          <div class="settings-section">
            <h3>行为</h3>
            <div class="setting-item">
              <n-switch
                v-model:value="appSettings.autoScroll"
                @update:value="updateAppSettings"
              />
              <label>自动滚动到底部</label>
            </div>
            
            <div class="setting-item">
              <n-switch
                v-model:value="appSettings.saveHistory"
                @update:value="updateAppSettings"
              />
              <label>保存聊天历史</label>
            </div>
          </div>
        </n-tab-pane>

        <!-- AI提供商 -->
        <n-tab-pane name="providers" tab="AI提供商">
          <AIProviderSettings />
        </n-tab-pane>

        <!-- AI设置 -->
        <n-tab-pane name="ai" tab="模型参数">
          <div class="settings-section">
            <h3>模型参数</h3>
            <div class="setting-item">
              <label>温度 ({{ chatSettings.temperature }})</label>
              <n-slider
                v-model:value="chatSettings.temperature"
                :min="0"
                :max="2"
                :step="0.1"
                @update:value="updateChatSettings"
              />
              <small>控制回答的随机性，值越高越有创意</small>
            </div>

            <div class="setting-item">
              <label>最大Token数</label>
              <n-input-number
                v-model:value="chatSettings.maxTokens"
                :min="100"
                :max="4000"
                @update:value="updateChatSettings"
              />
            </div>

            <div class="setting-item">
              <label>系统提示</label>
              <n-input
                v-model:value="chatSettings.systemPrompt"
                type="textarea"
                :autosize="{ minRows: 3, maxRows: 6 }"
                placeholder="输入系统提示词..."
                @update:value="updateChatSettings"
              />
            </div>
          </div>
        </n-tab-pane>

        <!-- 关于 -->
        <n-tab-pane name="about" tab="关于">
          <div class="about-section">
            <div class="app-info">
              <div class="app-icon">🤖</div>
              <h2>AI桌面客户端</h2>
              <p class="version">版本 1.0.0</p>
              <p class="description">
                基于Tauri + Vue3构建的高性能AI桌面应用
              </p>
            </div>
            
            <div class="tech-stack">
              <h3>技术栈</h3>
              <div class="tech-items">
                <div class="tech-item">
                  <strong>前端:</strong> Vue 3 + TypeScript + Naive UI
                </div>
                <div class="tech-item">
                  <strong>后端:</strong> Rust + Tauri
                </div>
                <div class="tech-item">
                  <strong>构建:</strong> Vite + UnoCSS
                </div>
              </div>
            </div>
            
            <div class="actions">
              <n-button @click="checkUpdate">检查更新</n-button>
              <n-button @click="openGithub" secondary>GitHub</n-button>
            </div>
          </div>
        </n-tab-pane>
      </n-tabs>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useMessage } from 'naive-ui'
import { useSettingsStore } from '@/stores/settings'
import AIProviderSettings from '@/components/Settings/AIProviderSettings.vue'

const message = useMessage()
const settingsStore = useSettingsStore()

// 计算属性
const appSettings = computed(() => settingsStore.app)
const chatSettings = computed(() => settingsStore.chat)

// 选项配置
const themeOptions = [
  { label: '跟随系统', value: 'auto' },
  { label: '浅色模式', value: 'light' },
  { label: '深色模式', value: 'dark' }
]

const fontSizeMarks = {
  12: '小',
  14: '中',
  16: '大',
  18: '特大',
  20: '超大'
}

// 方法
const updateAppSettings = () => {
  settingsStore.saveSettings()
}

const updateChatSettings = () => {
  settingsStore.saveSettings()
}

const checkUpdate = () => {
  message.info('当前已是最新版本')
}

const openGithub = () => {
  window.open('https://github.com/your-repo/ai-desktop-client', '_blank')
}
</script>

<style scoped>
.settings-view {
  height: 100%;
  overflow-y: auto;
  background: #f9fafb;
}

.settings-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 40px 20px;
}

.settings-header {
  margin-bottom: 32px;
  text-align: center;
}

.settings-header h1 {
  font-size: 32px;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 8px;
}

.settings-header p {
  font-size: 16px;
  color: #6b7280;
}

.settings-section {
  margin-bottom: 32px;
}

.settings-section h3 {
  font-size: 18px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e5e7eb;
}

.setting-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 24px;
}

.setting-item label {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.setting-item small {
  font-size: 12px;
  color: #9ca3af;
}

/* 开关样式特殊处理 */
.setting-item:has(.n-switch) {
  flex-direction: row;
  align-items: center;
  gap: 12px;
}

.setting-item:has(.n-switch) label {
  order: 2;
  margin: 0;
}

/* 关于页面 */
.about-section {
  text-align: center;
  padding: 40px 20px;
}

.app-info {
  margin-bottom: 40px;
}

.app-icon {
  font-size: 64px;
  margin-bottom: 16px;
}

.app-info h2 {
  font-size: 28px;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 8px;
}

.version {
  font-size: 16px;
  color: #6b7280;
  margin-bottom: 16px;
}

.description {
  font-size: 16px;
  color: #9ca3af;
  line-height: 1.6;
}

.tech-stack {
  margin-bottom: 40px;
}

.tech-stack h3 {
  font-size: 20px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 16px;
}

.tech-items {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-width: 400px;
  margin: 0 auto;
}

.tech-item {
  text-align: left;
  padding: 12px 16px;
  background: white;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.actions {
  display: flex;
  justify-content: center;
  gap: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .settings-container {
    padding: 20px 16px;
  }
  
  .settings-header h1 {
    font-size: 28px;
  }
  
  .tech-items {
    max-width: none;
  }
  
  .actions {
    flex-direction: column;
    align-items: center;
  }
}
</style>
