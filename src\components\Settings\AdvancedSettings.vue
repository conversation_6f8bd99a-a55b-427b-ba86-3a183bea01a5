<template>
  <div class="advanced-settings">
    <div class="settings-section">
      <div class="section-header">
        <h4>高级设置</h4>
        <p>开发者选项和高级功能</p>
      </div>
      
      <div class="section-content">
        <div class="setting-item">
          <div class="setting-label">
            <label>开发者模式</label>
            <small>启用调试功能和开发者工具</small>
          </div>
          <div class="setting-control">
            <n-switch v-model:value="developerMode" />
          </div>
        </div>

        <div class="setting-item">
          <div class="setting-label">
            <label>详细日志</label>
            <small>记录详细的应用运行日志</small>
          </div>
          <div class="setting-control">
            <n-switch v-model:value="verboseLogging" />
          </div>
        </div>

        <div class="setting-item">
          <div class="setting-label">
            <label>实验性功能</label>
            <small>启用实验性功能（可能不稳定）</small>
          </div>
          <div class="setting-control">
            <n-switch v-model:value="experimentalFeatures" />
          </div>
        </div>

        <div class="setting-item">
          <div class="setting-label">
            <label>导出设置</label>
            <small>导出当前配置到文件</small>
          </div>
          <div class="setting-control">
            <n-button @click="exportSettings" secondary>
              <template #icon>
                <i class="i-carbon-download" />
              </template>
              导出
            </n-button>
          </div>
        </div>

        <div class="setting-item">
          <div class="setting-label">
            <label>导入设置</label>
            <small>从文件导入配置</small>
          </div>
          <div class="setting-control">
            <n-button @click="importSettings" secondary>
              <template #icon>
                <i class="i-carbon-upload" />
              </template>
              导入
            </n-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useMessage } from 'naive-ui'

const message = useMessage()

// 响应式数据
const developerMode = ref(false)
const verboseLogging = ref(false)
const experimentalFeatures = ref(false)

// 方法
const exportSettings = () => {
  message.info('导出功能开发中...')
}

const importSettings = () => {
  message.info('导入功能开发中...')
}
</script>

<style scoped>
.advanced-settings {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.settings-section {
  background: white;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  overflow: hidden;
}

.section-header {
  padding: 20px 24px;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

.section-header h4 {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 4px 0;
}

.section-header p {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
}

.section-content {
  padding: 24px;
}

.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 0;
  border-bottom: 1px solid #f1f5f9;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-label {
  flex: 1;
  margin-right: 24px;
}

.setting-label label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 4px;
}

.setting-label small {
  font-size: 12px;
  color: #9ca3af;
  line-height: 1.4;
}

.setting-control {
  flex: 0 0 auto;
}
</style>
