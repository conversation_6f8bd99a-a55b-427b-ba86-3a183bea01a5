<template>
  <div class="chat-sidebar" :class="{ collapsed }">
    <!-- 顶部操作按钮 -->
    <div class="sidebar-header">
      <div class="header-buttons">
        <n-button
          type="primary"
          @click="createNewChat"
          :disabled="isLoading"
          class="action-btn primary-btn"
        >
          <template #icon>
            <n-icon :component="PlusIcon" />
          </template>
          <span v-if="!collapsed">新建对话</span>
        </n-button>

        <n-button
          v-if="!collapsed"
          quaternary
          @click="showCreateGroupModal = true"
          class="action-btn secondary-btn"
        >
          <template #icon>
            <n-icon :component="FolderPlusIcon" />
          </template>
          新建分组
        </n-button>
      </div>
    </div>

    <!-- 搜索框 -->
    <div v-if="!collapsed" class="sidebar-search">
      <n-input
        v-model:value="searchQuery"
        placeholder="搜索对话..."
        clearable
        size="small"
      >
        <template #prefix>
          <n-icon :component="SearchIcon" />
        </template>
      </n-input>
    </div>



    <!-- 分组标签区域 -->
    <div v-if="!collapsed" class="group-tags-area">
      <div class="group-tags-container" style="max-height: 119px; overflow-y: auto;">
        <div class="group-tags">
          <!-- 未分组标签 -->
          <div class="group-tag-wrapper">
            <div
              class="group-tag"
              :class="{ 
                active: selectedGroupId === null,
                'drop-target': dragOverGroupId === null
              }"
              @click="() => { selectedGroupId = null; chatStore.selectGroup(null) }"
              @dragover="(e) => handleDragOver(e, null)"
              @dragleave="handleDragLeave"
              @drop="(e) => handleDrop(e, null)"
            >
              <div class="group-tag-info">
                <span class="group-tag-icon">📄</span>
                <span class="group-tag-name">未分组</span>
              </div>
              <span class="group-tag-count">{{ ungroupedConversations.length }}</span>
            </div>
          </div>

          <!-- 分组标签 -->
          <!-- 分组标签 -->
          <div
            v-for="group in sortedGroups"
            :key="group.id"
            class="group-tag-wrapper"
          >
            <div
              class="group-tag"
              :class="{ 
                active: selectedGroupId === group.id,
                'drop-target': dragOverGroupId === group.id
              }"
              :style="{ borderLeftColor: group.color }"
              @click="() => { selectedGroupId = group.id; chatStore.selectGroup(group.id) }"
              @dragover="(e) => handleDragOver(e, group.id)"
              @dragleave="handleDragLeave"
              @drop="(e) => handleDrop(e, group.id)"
            >
              <div class="group-tag-info">
                <span class="group-tag-icon">{{ group.icon || '📁' }}</span>
                <span class="group-tag-name" :title="group.name">{{ group.name }}</span>
              </div>
              <span class="group-tag-count">{{ getGroupConversationCount(group.id) }}</span>
            </div>
            
            <!-- 外部操作按钮 -->
            <div class="group-tag-menu-external" style="pointer-events: none;">
              <n-dropdown
                :options="getGroupMenuOptions(group)"
                @select="(key) => handleGroupAction(key, group)"
                trigger="click"
              >
                <n-button text circle size="tiny" class="group-tag-menu" @click.stop style="pointer-events: auto;">
                  <template #icon>
                    <n-icon :component="SettingsIcon" />
                  </template>
                </n-button>
              </n-dropdown>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 对话列表 -->
    <div class="conversation-list">
      <n-scrollbar class="list-scrollbar" style="height: 100%;">
        <!-- 当前选中分组的对话 -->
        <div class="conversation-items">
          <ConversationItem
            v-for="conversation in currentGroupConversations"
            :key="conversation.id"
            :conversation="conversation"
            :active="conversation.id === currentConversation?.id"
            :collapsed="collapsed"
            :group-id="selectedGroupId"
            @select="selectConversation"
            @delete="deleteConversation"
            @pin="togglePin"
            @archive="toggleArchive"
            @rename="renameConversation"
            @move-to-group="moveConversationToGroup"
          />
        </div>

        <!-- 空状态 -->
        <div v-if="currentGroupConversations.length === 0" class="empty-state">
          <div class="empty-icon">💬</div>
          <div v-if="!collapsed" class="empty-text">
            {{ selectedGroupId ? '该分组暂无对话' : '还没有对话记录' }}
          </div>
        </div>
      </n-scrollbar>
    </div>

    <!-- 底部操作区 -->
    <div class="sidebar-footer">
      <!-- 统计信息 -->
      <div v-if="!collapsed" class="stats">
        <span class="stat-item">
          {{ conversations.length }} 个对话
        </span>
        <span class="stat-item">
          {{ totalMessages }} 条消息
        </span>
      </div>

      <!-- 操作按钮 -->
      <div class="footer-actions">
        <n-dropdown
          :options="moreOptions"
          @select="handleMoreAction"
          trigger="click"
        >
          <n-button quaternary circle size="small">
            <template #icon>
              <n-icon :component="MoreIcon" />
            </template>
          </n-button>
        </n-dropdown>

        <n-button
          quaternary
          circle
          size="small"
          @click="toggleArchiveView"
          :type="showArchived ? 'primary' : 'default'"
        >
          <template #icon>
            <n-icon :component="ArchiveIcon" />
          </template>
        </n-button>
      </div>
    </div>

    <!-- 创建分组模态框 -->
    <n-modal v-model:show="showCreateGroupModal" preset="dialog" title="创建新分组">
      <template #header>
        <div>创建新分组</div>
      </template>
      <div class="create-group-form">
        <n-form ref="createGroupFormRef" :model="createGroupForm" :rules="createGroupRules">
          <n-form-item label="分组名称" path="name">
            <n-input
              v-model:value="createGroupForm.name"
              placeholder="请输入分组名称"
              maxlength="20"
              show-count
            />
          </n-form-item>
          <n-form-item label="分组图标" path="icon">
            <div class="icon-selector">
              <div
                v-for="icon in groupIcons"
                :key="icon"
                class="icon-option"
                :class="{ active: createGroupForm.icon === icon }"
                @click="createGroupForm.icon = icon"
              >
                {{ icon }}
              </div>
            </div>
          </n-form-item>
          <n-form-item label="分组颜色" path="color">
            <div class="color-selector">
              <div
                v-for="color in groupColors"
                :key="color"
                class="color-option"
                :class="{ active: createGroupForm.color === color }"
                :style="{ backgroundColor: color }"
                @click="createGroupForm.color = color"
              ></div>
            </div>
          </n-form-item>
        </n-form>
      </div>
      <template #action>
        <n-space>
          <n-button @click="showCreateGroupModal = false">取消</n-button>
          <n-button type="primary" @click="handleCreateGroup">创建</n-button>
        </n-space>
      </template>
    </n-modal>

    <!-- 重命名分组模态框 -->
    <n-modal v-model:show="showRenameGroupModal" preset="dialog" title="重命名分组">
      <n-input
        v-model:value="renameGroupForm.name"
        placeholder="请输入新的分组名称"
        maxlength="20"
        show-count
        @keyup.enter="handleRenameGroup"
      />
      <template #action>
        <n-space>
          <n-button @click="showRenameGroupModal = false">取消</n-button>
          <n-button type="primary" @click="handleRenameGroup">确认</n-button>
        </n-space>
      </template>
    </n-modal>

    <!-- 更改颜色模态框 -->
    <n-modal v-model:show="showChangeColorModal" preset="dialog" title="更改分组颜色">
      <div class="color-selector">
        <div
          v-for="color in groupColors"
          :key="color"
          class="color-option"
          :class="{ active: changeColorForm.color === color }"
          :style="{ backgroundColor: color }"
          @click="changeColorForm.color = color"
        ></div>
      </div>
      <template #action>
        <n-space>
          <n-button @click="showChangeColorModal = false">取消</n-button>
          <n-button type="primary" @click="handleChangeColor">确认</n-button>
        </n-space>
      </template>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useDialog, useMessage } from 'naive-ui'
import {
  Plus as PlusIcon,
  Search as SearchIcon,
  MoreHorizontal as MoreIcon,
  MoreVertical as MoreVerticalIcon,
  Archive as ArchiveIcon,
  FolderPlus as FolderPlusIcon,
  ChevronRight as ChevronRightIcon,
  ChevronDown as ChevronDownIcon,
  MessageSquare as MessageSquareIcon,
  Settings as SettingsIcon,
  Edit3 as EditIcon
} from 'lucide-vue-next'
import { useChatStore } from '@/stores/chat'
import { useSettingsStore } from '@/stores/settings'
import ConversationItem from './ConversationItem.vue'

const props = defineProps<{
  collapsed: boolean
}>()

const dialog = useDialog()
const message = useMessage()
const chatStore = useChatStore()
const settingsStore = useSettingsStore()

// 响应式数据
const searchQuery = ref('')
const showArchived = ref(false)
const showCreateGroupModal = ref(false)
const showRenameGroupModal = ref(false)
const showChangeColorModal = ref(false)
const selectedGroupId = ref<string | null>(null)
const editingGroup = ref<any>(null)

// 创建分组表单
const createGroupFormRef = ref()
const createGroupForm = ref({
  name: '',
  icon: '📁',
  color: '#3b82f6'
})

const renameGroupForm = ref({
  name: ''
})

const changeColorForm = ref({
  color: '#3b82f6'
})

const createGroupRules = {
  name: [
    { required: true, message: '请输入分组名称', trigger: 'blur' },
    { min: 1, max: 20, message: '分组名称长度应为1-20个字符', trigger: 'blur' }
  ]
}

// 图标和颜色选项
const groupIcons = ['📁', '💻', '📚', '🏠', '📊', '🎨', '🔧', '💡', '🎯', '🚀', '📝', '🎵']
const groupColors = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6', '#06b6d4', '#84cc16', '#f97316']

// 计算属性
const conversations = computed(() => chatStore.sortedConversations)
const conversationGroups = computed(() => chatStore.conversationGroups)
const sortedGroups = computed(() => chatStore.sortedGroups)
const conversationsByGroup = computed(() => chatStore.conversationsByGroup)
const currentConversation = computed(() => chatStore.currentConversation)
const isLoading = computed(() => chatStore.isLoading)

// 分组相关计算属性
const ungroupedConversations = computed(() => {
  const searchLower = searchQuery.value.toLowerCase()
  return conversationsByGroup.value.ungrouped.filter(conv => {
    // 归档状态过滤
    const archiveMatch = showArchived.value ? conv.isArchived : !conv.isArchived
    if (!archiveMatch) return false
    
    // 搜索过滤
    if (searchQuery.value && !conv.title.toLowerCase().includes(searchLower)) {
      return false
    }
    
    return true
  })
})

// 当前选中分组的对话
const currentGroupConversations = computed(() => {
  if (selectedGroupId.value === null) {
    return ungroupedConversations.value
  } else {
    return getGroupConversations(selectedGroupId.value)
  }
})



const totalMessages = computed(() => {
  return conversations.value.reduce((total, conv) => total + conv.messageCount, 0)
})

// 更多操作选项
const moreOptions = computed(() => [
  {
    label: '导出对话',
    key: 'export',
    icon: () => '📤'
  },
  {
    label: '导入对话',
    key: 'import',
    icon: () => '📥'
  },
  {
    type: 'divider',
    key: 'divider1'
  },
  {
    label: '清空所有对话',
    key: 'clear',
    icon: () => '🗑️'
  }
])

// 方法
const createNewChat = async () => {
  try {
    console.log('🆕 创建新对话...')

    // 获取第一个启用的提供商的第一个模型作为默认模型
    const enabledProvider = settingsStore.providers.find(p => p.enabled)
    const defaultModel = enabledProvider?.models[0]?.id || 'deepseek-reasoner'

    // 修复：正确的参数顺序 (title, groupId, model, inheritSettings)
    const newConversation = chatStore.createConversation(
      '新对话',
      selectedGroupId.value || undefined,
      defaultModel,
      settingsStore.chat // 传递全局设置作为默认值
    )

    console.log('✅ 新对话创建成功:', newConversation.title)

    // 选择新创建的对话
    await chatStore.selectConversation(newConversation.id)

    console.log('✅ 已切换到新对话')
  } catch (error) {
    console.error('❌ 创建新对话失败:', error)
  }
}

const selectConversation = (conversationId: string) => {
  chatStore.selectConversation(conversationId)
}

const deleteConversation = (conversationId: string) => {
  dialog.warning({
    title: '确认删除',
    content: '确定要删除这个对话吗？此操作无法撤销。',
    positiveText: '删除',
    negativeText: '取消',
    onPositiveClick: () => {
      chatStore.deleteConversation(conversationId)
      message.success('对话已删除')
    }
  })
}

const togglePin = (conversationId: string) => {
  chatStore.togglePinConversation(conversationId)
  const conversation = conversations.value.find(c => c.id === conversationId)
  if (conversation) {
    message.success(conversation.isPinned ? '已取消置顶' : '已置顶')
  }
}

const toggleArchive = (conversationId: string) => {
  chatStore.toggleArchiveConversation(conversationId)
  const conversation = conversations.value.find(c => c.id === conversationId)
  if (conversation) {
    message.success(conversation.isArchived ? '已取消归档' : '已归档')
  }
}

// 分组相关方法
const getGroupConversations = (groupId: string) => {
  const groupConversations = conversationsByGroup.value.grouped[groupId] || []
  const searchLower = searchQuery.value.toLowerCase()
  
  return groupConversations.filter(conv => {
    // 归档状态过滤
    const archiveMatch = showArchived.value ? conv.isArchived : !conv.isArchived
    if (!archiveMatch) return false
    
    // 搜索过滤
    if (searchQuery.value && !conv.title.toLowerCase().includes(searchLower)) {
      return false
    }
    
    return true
  })
}

const getGroupConversationCount = (groupId: string) => {
  return getGroupConversations(groupId).length
}

const toggleGroupCollapse = (groupId: string) => {
  chatStore.toggleGroupCollapse(groupId)
}

const moveConversationToGroup = (conversationId: string, groupId?: string) => {
  chatStore.moveConversationToGroup(conversationId, groupId)
  message.success('对话已移动')
}

const getGroupMenuOptions = (group: any) => [
  {
    label: '重命名分组',
    key: 'rename',
    icon: () => '✏️',
    props: {
      style: 'color: #374151;'
    }
  },
  {
    label: '更改颜色',
    key: 'color',
    icon: () => '🎨',
    props: {
      style: 'color: #374151;'
    }
  },
  {
    type: 'divider',
    key: 'divider'
  },
  {
    label: '删除分组',
    key: 'delete',
    icon: () => '🗑️',
    props: {
      style: 'color: #ef4444;'
    }
  }
]

const handleGroupAction = (key: string, group: any) => {
  switch (key) {
    case 'rename':
      showRenameGroupDialog(group)
      break
    case 'color':
      showChangeColorDialog(group)
      break
    case 'delete':
      dialog.warning({
        title: '确认删除',
        content: `确定要删除分组"${group.name}"吗？分组内的对话将移到未分组。`,
        positiveText: '删除',
        negativeText: '取消',
        onPositiveClick: () => {
          chatStore.deleteGroup(group.id)
          if (selectedGroupId.value === group.id) {
            selectedGroupId.value = null
            chatStore.selectGroup(null)
          }
          message.success('分组已删除')
        }
      })
      break
  }
}

const renameConversation = (conversationId: string, newTitle: string) => {
  chatStore.updateConversation(conversationId, { title: newTitle })
  message.success('重命名成功')
}

const showRenameGroupDialog = (group: any) => {
  editingGroup.value = group
  renameGroupForm.value.name = group.name
  showRenameGroupModal.value = true
}

const showChangeColorDialog = (group: any) => {
  editingGroup.value = group
  changeColorForm.value.color = group.color
  showChangeColorModal.value = true
}

const handleRenameGroup = () => {
  if (editingGroup.value && renameGroupForm.value.name.trim()) {
    chatStore.updateGroup(editingGroup.value.id, { name: renameGroupForm.value.name.trim() })
    message.success('分组重命名成功')
    showRenameGroupModal.value = false
  }
}

const handleChangeColor = () => {
  if (editingGroup.value) {
    chatStore.updateGroup(editingGroup.value.id, { color: changeColorForm.value.color })
    message.success('分组颜色已更改')
    showChangeColorModal.value = false
  }
}

// 拖拽状态
const dragOverGroupId = ref<string | null | undefined>(undefined)

// 拖放处理
const handleDragOver = (event: DragEvent, groupId: string | null) => {
  event.preventDefault()
  event.stopPropagation()
  if (event.dataTransfer) {
    event.dataTransfer.dropEffect = 'move'
  }
  console.log('拖拽悬停在分组:', groupId || '未分组', 'target:', event.target)
  dragOverGroupId.value = groupId
}

const handleDragLeave = () => {
  // 延迟清除状态，避免在分组标签之间移动时闪烁
  setTimeout(() => {
    dragOverGroupId.value = undefined
  }, 50)
}

const handleDrop = (event: DragEvent, groupId: string | null) => {
  event.preventDefault()
  event.stopPropagation()
  console.log('拖放到分组:', groupId || '未分组')
  dragOverGroupId.value = undefined
  
  if (event.dataTransfer) {
    const conversationData = event.dataTransfer.getData('application/conversation')
    console.log('拖放数据:', conversationData)
    if (conversationData) {
      try {
        const conversation = JSON.parse(conversationData)
        console.log('解析的对话数据:', conversation)
        if (conversation.groupId !== groupId) {
          console.log('移动对话从', conversation.groupId, '到', groupId)
          chatStore.moveConversationToGroup(conversation.id, groupId || undefined)
          message.success(`对话已移动到${groupId ? '分组' : '未分组'}`)
        } else {
          console.log('对话已在目标分组中')
        }
      } catch (error) {
        console.error('拖放处理失败:', error)
      }
    }
  }
}

// 创建分组处理
const handleCreateGroup = async () => {
  try {
    await createGroupFormRef.value?.validate()

    await chatStore.createGroup(
      createGroupForm.value.name,
      createGroupForm.value.color,
      createGroupForm.value.icon
    )

    message.success('分组创建成功')
    showCreateGroupModal.value = false

    // 重置表单
    createGroupForm.value = {
      name: '',
      icon: '📁',
      color: '#3b82f6'
    }
  } catch (error) {
    console.error('创建分组失败:', error)
  }
}

// 初始化
const initializeSidebar = async () => {
  console.log('🚀 初始化侧边栏...')
  // 初始化chatStore，加载存储的数据
  await chatStore.initialize()
  selectedGroupId.value = null
  chatStore.selectGroup(null)
  console.log('✅ 侧边栏初始化完成')
}

// 组件挂载时初始化
onMounted(() => {
  initializeSidebar()
})

const toggleArchiveView = () => {
  showArchived.value = !showArchived.value
}

const handleMoreAction = (key: string) => {
  switch (key) {
    case 'export':
      exportConversations()
      break
    case 'import':
      importConversations()
      break
    case 'clear':
      clearAllConversations()
      break
  }
}

const exportConversations = () => {
  // 实现导出功能
  message.info('导出功能开发中...')
}

const importConversations = () => {
  // 实现导入功能
  message.info('导入功能开发中...')
}

const clearAllConversations = () => {
  dialog.warning({
    title: '确认清空',
    content: '确定要清空所有对话吗？此操作无法撤销。',
    positiveText: '清空',
    negativeText: '取消',
    onPositiveClick: () => {
      chatStore.clearAllData()
      message.success('已清空所有对话')
    }
  })
}
</script>

<style scoped>
.chat-sidebar {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #fafafa;
  transition: all 0.3s ease;
  overflow: hidden;
}

.sidebar-header {
  padding: 12px;
  border-bottom: 1px solid #e5e7eb;
}



.sidebar-search {
  padding: 8px 12px;
  border-bottom: 1px solid #e5e7eb;
  flex-shrink: 0;
}

.conversation-list {
  flex: 1;
  overflow: hidden;
  min-height: 0;
}

.list-scrollbar {
  width: 100%;
}

/* 顶部操作按钮 */
.sidebar-header {
  padding: 12px;
  border-bottom: 1px solid #e5e7eb;
  flex-shrink: 0;
}

.header-buttons {
  display: flex;
  gap: 8px;
}

.action-btn {
  flex: 1;
  height: 36px;
  font-size: 13px;
}

.primary-btn {
  flex: 2;
}

.secondary-btn {
  flex: 1;
  border: 1px solid #d1d5db;
}

/* 分组标签区域 */
.group-tags-area {
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
  flex-shrink: 0;
}

.group-tags-container {
  width: 100%;
}

.group-tags-container::-webkit-scrollbar {
  width: 6px;
}

.group-tags-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.group-tags-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.group-tags-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.group-tags-scrollbar {
  width: 100%;
}

.group-tags {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3px;
  padding: 4px 12px 8px 6px;
}

.group-tag-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.group-tag-wrapper .group-tag {
  pointer-events: auto;
}

.group-tag {
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-radius: 6px;
  transition: all 0.2s;
  background: white;
  border: 1px solid #e5e7eb;
  border-left: 3px solid #e5e7eb;
  min-height: 36px;
  overflow: hidden;
  flex: 1;
  cursor: pointer;
  padding: 6px 14px 6px 5px;
  position: relative;
  pointer-events: auto;
}

.group-tag:hover {
  background: #f3f4f6;
  border-color: #d1d5db;
}

.group-tag.active {
  background: #eff6ff;
  border-color: #3b82f6;
  color: #1d4ed8;
}

.group-tag-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex: 1;
  padding: 6px 20px 6px 8px;
  cursor: pointer;
  min-width: 0;
}

.group-tag-menu-external {
  position: absolute;
  top: 2px;
  right: 2px;
  opacity: 0;
  transition: all 0.2s ease;
  z-index: 10;
  pointer-events: none;
}

.group-tag-wrapper:hover .group-tag-menu-external {
  opacity: 0.9;
}

.group-tag.active ~ .group-tag-menu-external {
  opacity: 0.7;
}

.group-tag-wrapper:hover .group-tag.active ~ .group-tag-menu-external {
  opacity: 1;
}

.group-tag-menu-external:hover {
  opacity: 1 !important;
}

.group-tag-menu {
  width: 16px;
  height: 16px;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.8);
  border: none;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12);
  backdrop-filter: blur(8px);
  transition: all 0.2s ease;
  color: #6b7280;
  pointer-events: auto;
}

.group-tag-menu:hover {
  background: rgba(255, 255, 255, 0.95);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
  transform: scale(1.05);
  color: #374151;
}

.group-tag-icon {
  font-size: 12px;
  margin-right: 3px;
  width: 12px;
  text-align: center;
  flex-shrink: 0;
  line-height: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.group-tag-info {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 0;
  margin-right: 1px;
}

.group-tag-name {
  font-size: 11px;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  min-width: 0;
  max-width: 50px;
}

.group-tag-count {
  font-size: 9px;
  color: #6b7280;
  background: #f3f4f6;
  padding: 1px 3px;
  border-radius: 4px;
  min-width: 10px;
  text-align: center;
  flex-shrink: 0;
  font-weight: 500;
  margin-left: 1px;
}

.group-tag.active .group-tag-count {
  background: #dbeafe;
  color: #1d4ed8;
}

.group-tag.drop-target {
  background: #f0f9ff !important;
  border-color: #0ea5e9 !important;
  border-left-color: #0ea5e9 !important;
  transform: scale(1.02);
  box-shadow: 0 2px 8px rgba(14, 165, 233, 0.3);
  animation: pulse 1s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.8; }
}





/* 创建分组模态框样式 */
.create-group-form {
  padding: 16px 0;
}

.icon-selector {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 8px;
  margin-top: 8px;
}

.icon-option {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: 2px solid #e5e7eb;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 16px;
}

.icon-option:hover {
  border-color: #3b82f6;
}

.icon-option.active {
  border-color: #3b82f6;
  background: #eff6ff;
}

.color-selector {
  display: flex;
  gap: 8px;
  margin-top: 8px;
}

.color-option {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  cursor: pointer;
  border: 2px solid transparent;
  transition: all 0.2s;
}

.color-option:hover {
  transform: scale(1.1);
}

.color-option.active {
  border-color: #374151;
  transform: scale(1.1);
}

.conversation-items {
  padding: 8px 0;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #9ca3af;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 12px;
}

.empty-text {
  font-size: 14px;
  text-align: center;
  line-height: 1.5;
}

.sidebar-footer {
  padding: 12px 16px;
  border-top: 1px solid #e5e7eb;
  background: #f9fafb;
  flex-shrink: 0;
}

.stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
  font-size: 12px;
  color: #6b7280;
}

.stat-item {
  white-space: nowrap;
}

.footer-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 折叠状态样式 */
.chat-sidebar.collapsed {
  .sidebar-header {
    padding: 12px 8px;
  }

  .new-chat-btn {
    width: 32px;
    padding: 0;
  }
  
  .sidebar-footer {
    padding: 12px;
  }
  
  .footer-actions {
    flex-direction: column;
    gap: 8px;
  }
}
</style>
