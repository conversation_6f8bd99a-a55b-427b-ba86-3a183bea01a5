<template>
  <div class="msg" :class="messageClass">
    <div class="avatar" :class="avatarClass">
      <component :is="avatarIcon" />
    </div>
    <div class="content">
      <div class="header">
        <span class="role">{{ roleText }}</span>
        <span class="time">{{ formatTime(message.timestamp) }}</span>
        <div class="actions">
          <button @click="copyMessage" class="btn" :disabled="copyLoading">
            <component :is="copySuccess ? CheckIcon : CopyIcon" />
          </button>
          <button v-if="message.role === 'assistant'" @click="regenerateMessage" class="btn">
            <component :is="RefreshIcon" />
          </button>
        </div>
      </div>


      <!-- 内容渲染区域 -->
      <div class="message-content">
        <!-- 思考过程（仅思考模型显示） -->
        <div v-if="message.isReasoningModel && (message.reasoning || !message.reasoningComplete)" class="reasoning-section">
          <div class="reasoning-header" @click="toggleReasoning">
            <div class="reasoning-left">
              <n-icon :component="ZapIcon" class="reasoning-icon" />
              <span class="reasoning-title">思考过程</span>
              <div v-if="!message.reasoningComplete && streaming" class="reasoning-status">
                <n-spin size="small" />
              </div>
            </div>

            <div class="reasoning-right">
              <!-- 思考统计信息 -->
              <div v-if="message.reasoningComplete && reasoningStats" class="reasoning-stats">
                <span class="stat-item">{{ reasoningStats.duration }}</span>
                <span class="stat-separator">•</span>
                <span class="stat-item">{{ reasoningStats.tokens }} tokens</span>
              </div>

              <!-- 复制按钮 -->
              <n-button
                v-if="message.reasoning"
                size="tiny"
                quaternary
                circle
                class="copy-reasoning-btn"
                @click.stop="copyReasoning"
                :loading="copyReasoningLoading"
              >
                <template #icon>
                  <n-icon :component="copyReasoningSuccess ? CheckIcon : CopyIcon" />
                </template>
              </n-button>

              <!-- 折叠按钮 -->
              <div class="reasoning-toggle">
                <n-icon :component="reasoningCollapsed ? ChevronRightIcon : ChevronDownIcon" class="toggle-icon" />
              </div>
            </div>
          </div>

          <div v-show="!reasoningCollapsed" class="reasoning-content">
            <div
              class="reasoning-viewport"
              :class="{ 'expanded': reasoningExpanded }"
            >
              <div
                class="reasoning-text"
                ref="reasoningTextRef"
              >
                <span v-if="message.reasoning">{{ message.reasoning }}</span>
                <span v-if="!message.reasoningComplete && streaming" class="thinking-indicator">
                  <span class="thinking-dots">思考中</span>
                </span>
              </div>
            </div>

            <!-- 展开/收起按钮 -->
            <div v-if="message.reasoning && message.reasoningComplete" class="reasoning-expand-btn">
              <n-button
                size="tiny"
                text
                @click="toggleReasoningExpand"
                class="expand-toggle"
              >
                {{ reasoningExpanded ? '收起' : '展开全部' }}
              </n-button>
            </div>
          </div>
        </div>

        <!-- 主要回答内容 -->
        <div class="main-content">
          <!-- 普通内容 -->
          <div v-if="!shouldUseChunking" class="text" ref="messageTextRef" v-html="displayContent" />

          <!-- 分块内容 -->
          <div v-else class="chunked-content">
            <div class="text" ref="messageTextRef" v-html="displayContent" />

            <!-- 加载更多按钮 -->
            <div v-if="hasMoreContent" class="load-more-content">
              <n-button
                @click="loadMoreContent"
                size="small"
                secondary
                class="load-more-btn"
              >
                显示更多内容 (剩余 {{ remainingChunks }} 块)
              </n-button>
            </div>
          </div>
        </div>
      </div>

      <div v-if="streaming" class="status">
        <span class="cursor">|</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick, watch, withDefaults } from 'vue'
import { useMessage } from 'naive-ui'
import {
  Copy as CopyIcon,
  RefreshCw as RefreshIcon,
  Check as CheckIcon,
  User as UserIcon,
  Bot as BotIcon,
  Settings as SystemIcon,
  Zap as ZapIcon,
  ChevronRight as ChevronRightIcon,
  ChevronDown as ChevronDownIcon
} from 'lucide-vue-next'

interface Message {
  id: string
  role: 'user' | 'assistant' | 'system'
  content: string
  timestamp?: Date | string
  model?: string
  tokens?: number
  // 思考模型支持
  reasoning?: string
  isReasoningModel?: boolean
  reasoningComplete?: boolean
}

const props = withDefaults(defineProps<{
  message: Message
  streaming?: boolean
  streamingMessageId?: string | null
}>(), {
  streaming: false,
  streamingMessageId: null
})

const emit = defineEmits<{
  regenerate: [messageId: string]
}>()

const messageApi = useMessage()
const copyLoading = ref(false)
const copySuccess = ref(false)
const renderedContent = ref('')
const asyncRenderer = ref<any>(null)
const messageTextRef = ref<HTMLElement>()
const reasoningTextRef = ref<HTMLElement>()
const reasoningCollapsed = ref(false)
const reasoningExpanded = ref(false)
const copyReasoningLoading = ref(false)
const copyReasoningSuccess = ref(false)
const reasoningStartTime = ref<number | null>(null)
let copyTimer: NodeJS.Timeout | null = null
let copyReasoningTimer: NodeJS.Timeout | null = null

const messageClass = computed(() => ({
  'user': props.message.role === 'user',
  'assistant': props.message.role === 'assistant',
  'system': props.message.role === 'system'
}))

// 思考统计信息
const reasoningEndTime = ref<number | null>(null)

const reasoningStats = computed(() => {
  if (!props.message.reasoning) return null

  // 计算思考内容输出时间
  let duration = '0.0s'
  if (reasoningStartTime.value) {
    if (props.message.reasoningComplete && reasoningEndTime.value) {
      // 思考完成，使用结束时间
      duration = `${((reasoningEndTime.value - reasoningStartTime.value) / 1000).toFixed(1)}s`
    } else if (!props.message.reasoningComplete) {
      // 思考中，使用当前时间
      duration = `${((Date.now() - reasoningStartTime.value) / 1000).toFixed(1)}s`
    }
  }

  // 估算token数量（简单估算：中文字符/2 + 英文单词）
  const reasoning = props.message.reasoning
  const chineseChars = (reasoning.match(/[\u4e00-\u9fff]/g) || []).length
  const englishWords = (reasoning.match(/[a-zA-Z]+/g) || []).length
  const tokens = Math.round(chineseChars / 2 + englishWords)

  return {
    duration,
    tokens
  }
})

const avatarClass = computed(() => `avatar-${props.message.role}`)

const avatarIcon = computed(() => {
  switch (props.message.role) {
    case 'user': return UserIcon
    case 'assistant': return BotIcon
    case 'system': return SystemIcon
    default: return UserIcon
  }
})

const roleText = computed(() => {
  switch (props.message.role) {
    case 'user': return '用户'
    case 'assistant': return 'AI助手'
    case 'system': return '系统'
    default: return '未知'
  }
})

// 分块渲染配置
const CHUNK_SIZE = 2000 // 每块2000字符
const MAX_VISIBLE_CHUNKS = 5 // 最多显示5块
const shouldUseChunking = computed(() =>
  props.message.content.length > CHUNK_SIZE * 2
)

// 内容分块
const contentChunks = computed(() => {
  if (!shouldUseChunking.value) return []

  const content = props.message.content
  const chunks = []

  for (let i = 0; i < content.length; i += CHUNK_SIZE) {
    const chunk = content.slice(i, i + CHUNK_SIZE)
    chunks.push({
      id: `chunk-${i}`,
      content: chunk,
      startPos: i,
      endPos: Math.min(i + CHUNK_SIZE, content.length),
      rendered: false
    })
  }

  return chunks
})

// 可见的块
const visibleChunks = ref<number>(Math.min(MAX_VISIBLE_CHUNKS, contentChunks.value.length))

// 显示内容
const displayContent = computed(() => {
  // 分块渲染模式
  if (shouldUseChunking.value && contentChunks.value.length > 0) {
    const visibleContent = contentChunks.value
      .slice(0, visibleChunks.value)
      .map(chunk => chunk.content)
      .join('')

    if (renderedContent.value) {
      return renderedContent.value
    }
    return parseMarkdown(visibleContent)
  }

  // 普通渲染模式
  if (renderedContent.value) {
    return renderedContent.value
  }

  // 降级方案：使用简单解析器
  return parseMarkdown(props.message.content)
})

// 是否有更多内容
const hasMoreContent = computed(() =>
  shouldUseChunking.value && visibleChunks.value < contentChunks.value.length
)

// 剩余块数
const remainingChunks = computed(() =>
  Math.max(0, contentChunks.value.length - visibleChunks.value)
)

// 加载更多内容
const loadMoreContent = () => {
  const nextChunks = Math.min(3, contentChunks.value.length - visibleChunks.value)
  visibleChunks.value += nextChunks

  // 重新渲染内容
  if (asyncRenderer.value) {
    renderContentAsync()
  }
}



// 简单但可靠的Markdown解析器
const parseMarkdown = (content: string): string => {
  if (!content) return ''

  let html = content

  // 1. 先处理代码块，避免被其他规则影响
  html = html.replace(/```(\w+)?\s*\n([\s\S]*?)\n```/g, (match, lang, code) => {
    const language = lang || ''
    const trimmedCode = code.trim()
    return `<div class="code-block">
      <div class="code-header">
        <span class="code-lang">${language.toUpperCase()}</span>
        <button class="copy-code-btn" data-code="${encodeURIComponent(trimmedCode)}">
          <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
            <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
          </svg>
          <span class="copy-text">复制</span>
        </button>
      </div>
      <pre><code>${escapeHtml(trimmedCode)}</code></pre>
    </div>`
  })

  // 2. 处理标题（包括中文冒号）
  html = html.replace(/^#### (.+)$/gm, '<h4>$1</h4>')
  html = html.replace(/^### (.+)$/gm, '<h3>$1</h3>')
  html = html.replace(/^## (.+)$/gm, '<h2>$1</h2>')
  html = html.replace(/^# (.+)$/gm, '<h1>$1</h1>')

  // 3. 处理"方法X:"这样的标题
  html = html.replace(/^(方法\s*\d+\s*[:：]\s*.+)$/gm, '<h3>$1</h3>')
  html = html.replace(/^(\d+\.\s*.+)$/gm, '<h4>$1</h4>')

  // 4. 水平分隔线
  html = html.replace(/^---+$/gm, '<hr>')
  html = html.replace(/^\*\*\*+$/gm, '<hr>')
  html = html.replace(/^___+$/gm, '<hr>')

  // 5. 行内代码
  html = html.replace(/`([^`\n]+)`/g, '<code>$1</code>')

  // 6. 粗体和斜体
  html = html.replace(/\*\*([^*\n]+)\*\*/g, '<strong>$1</strong>')
  html = html.replace(/\*([^*\n]+)\*/g, '<em>$1</em>')

  // 7. 链接
  html = html.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank">$1</a>')

  // 8. 列表
  html = html.replace(/^[\s]*[-*+]\s+(.+)$/gm, '<li>$1</li>')
  html = html.replace(/^[\s]*\d+\.\s+(.+)$/gm, '<li>$1</li>')

  // 9. 包装连续的li为ul
  html = html.replace(/(<li>.*<\/li>(?:\s*<br>\s*<li>.*<\/li>)*)/g, '<ul>$1</ul>')

  // 10. 最后处理换行 - 避免过多空行
  html = html.replace(/\n\n+/g, '<br><br>')  // 多个换行变成两个br
  html = html.replace(/\n/g, '<br>')         // 单个换行变成一个br

  return html
}

// HTML转义
const escapeHtml = (text: string): string => {
  const div = document.createElement('div')
  div.textContent = text
  return div.innerHTML
}

const formatTime = (timestamp?: Date | string): string => {
  if (!timestamp) return ''
  const date = typeof timestamp === 'string' ? new Date(timestamp) : timestamp
  return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
}

const copyMessage = async () => {
  if (copyLoading.value) return

  try {
    copyLoading.value = true
    await navigator.clipboard.writeText(props.message.content)
    copySuccess.value = true
    messageApi.success('已复制')

    if (copyTimer) clearTimeout(copyTimer)
    copyTimer = setTimeout(() => {
      copySuccess.value = false
    }, 2000)
  } catch (error) {
    messageApi.error('复制失败')
  } finally {
    copyLoading.value = false
  }
}

// 代码复制处理
const handleCodeCopy = async (e: Event) => {
  e.preventDefault()
  e.stopPropagation()

  const btn = e.currentTarget as HTMLButtonElement
  const code = decodeURIComponent(btn.dataset.code || '')
  const copyText = btn.querySelector('.copy-text')

  if (!code || btn.disabled) return

  try {
    btn.disabled = true
    await navigator.clipboard.writeText(code)

    if (copyText) {
      copyText.textContent = '已复制'
    }
    btn.classList.add('copied')
    messageApi.success('代码已复制')

    setTimeout(() => {
      if (copyText) {
        copyText.textContent = '复制'
      }
      btn.classList.remove('copied')
      btn.disabled = false
    }, 2000)

  } catch (err) {
    console.error('复制失败:', err)
    btn.disabled = false
    messageApi.error('复制失败')
  }
}

const regenerateMessage = () => {
  emit('regenerate', props.message.id)
}

// 切换思考过程显示/隐藏
const toggleReasoning = () => {
  reasoningCollapsed.value = !reasoningCollapsed.value
}

// 切换思考内容展开/收起
const toggleReasoningExpand = () => {
  reasoningExpanded.value = !reasoningExpanded.value
}

// 复制思考过程
const copyReasoning = async () => {
  if (!props.message.reasoning || copyReasoningLoading.value) return

  try {
    copyReasoningLoading.value = true
    await navigator.clipboard.writeText(props.message.reasoning)

    copyReasoningSuccess.value = true
    messageApi.success('思考过程已复制')

    // 清除之前的定时器
    if (copyReasoningTimer) clearTimeout(copyReasoningTimer)

    // 2秒后重置状态
    copyReasoningTimer = setTimeout(() => {
      copyReasoningSuccess.value = false
    }, 2000)

  } catch (error) {
    console.error('复制思考过程失败:', error)
    messageApi.error('复制失败')
  } finally {
    copyReasoningLoading.value = false
  }
}

// 设置代码复制按钮事件监听器 - 防抖优化
let setupTimeout: NodeJS.Timeout | null = null
const setupCodeCopyHandlers = () => {
  // 清除之前的定时器，防止频繁调用
  if (setupTimeout) {
    clearTimeout(setupTimeout)
  }

  // 流式消息使用更短的防抖时间
  const isStreaming = props.streaming || props.message.id === props.streamingMessageId
  const delay = isStreaming ? 50 : 100

  setupTimeout = setTimeout(() => {
    nextTick(() => {
      if (!messageTextRef.value) return
      const copyButtons = messageTextRef.value.querySelectorAll('.copy-code-btn')
      copyButtons.forEach(button => {
        // 检查是否已经绑定过事件，避免重复绑定
        if (!button.hasAttribute('data-handler-bound')) {
          button.addEventListener('click', handleCodeCopy)
          button.setAttribute('data-handler-bound', 'true')
        }
      })
    })
  }, delay)
}

// 初始化异步渲染器
const initAsyncRenderer = async () => {
  try {
    const { useAsyncRenderer } = await import('@/composables/useAsyncRenderer')
    asyncRenderer.value = useAsyncRenderer()
    await renderContentAsync()
  } catch (error) {
    console.warn('异步渲染器加载失败，使用降级方案:', error)
  }
}

// 异步渲染内容
const renderContentAsync = async () => {
  if (!asyncRenderer.value || !props.message.content) return

  try {
    renderedContent.value = await asyncRenderer.value.renderMarkdown(props.message.content)
    nextTick(() => {
      setupCodeCopyHandlers()
    })
  } catch (error) {
    console.error('异步渲染失败:', error)
    renderedContent.value = parseMarkdown(props.message.content)
  }
}

// 生命周期钩子必须在setup顶层调用
onMounted(() => {
  initAsyncRenderer()
  setupCodeCopyHandlers()
})

onUnmounted(() => {
  // 清理所有定时器
  if (copyTimer) clearTimeout(copyTimer)
  if (copyReasoningTimer) clearTimeout(copyReasoningTimer)
  if (setupTimeout) clearTimeout(setupTimeout)
  if (contentUpdateTimeout) clearTimeout(contentUpdateTimeout)

  // 清理异步渲染器
  if (asyncRenderer.value && asyncRenderer.value.cleanup) {
    asyncRenderer.value.cleanup()
  }
})

// 简化渲染逻辑 - 稳定版本
let contentUpdateTimeout: NodeJS.Timeout | null = null

watch(() => props.message.content, (newContent, oldContent) => {
  if (!newContent || newContent === oldContent) return

  console.log('🔄 消息内容变化:', props.message.id, '长度:', newContent.length)

  const isStreaming = props.streaming || props.message.id === props.streamingMessageId
  console.log('🎯 流式状态:', isStreaming, 'streaming:', props.streaming, 'streamingMessageId:', props.streamingMessageId)

  // 清除之前的定时器
  if (contentUpdateTimeout) {
    clearTimeout(contentUpdateTimeout)
  }

  if (isStreaming) {
    // 流式消息：极短防抖，保持流式效果
    contentUpdateTimeout = setTimeout(() => {
      renderContent()
    }, 20)
  } else {
    // 非流式消息：立即渲染
    renderContent()
  }
}, { immediate: false })

// 监听思考过程内容变化，实现自动滚动效果
watch(() => props.message.reasoning, (newReasoning, oldReasoning) => {
  if (!props.message.isReasoningModel) return

  // 第一次收到思考内容时记录开始时间
  if (newReasoning && !oldReasoning) {
    reasoningStartTime.value = Date.now()

    // 思考过程显示时自动滚动到该消息
    nextTick(() => {
      const messageElement = messageTextRef.value?.closest('.virtual-item')
      if (messageElement) {
        messageElement.scrollIntoView({
          behavior: 'smooth',
          block: 'center'
        })
      }
    })
  }

  // 思考内容更新时，自动滚动到底部
  if (newReasoning && newReasoning !== oldReasoning) {
    nextTick(() => {
      if (reasoningTextRef.value) {
        reasoningTextRef.value.scrollTop = reasoningTextRef.value.scrollHeight
      }
    })
  }
}, { immediate: false })

// 监听思考完成状态，记录结束时间并自动折叠
watch(() => props.message.reasoningComplete, (isComplete) => {
  if (isComplete && props.message.isReasoningModel) {
    // 记录思考结束时间
    reasoningEndTime.value = Date.now()

    // 思考完成后延迟1秒自动折叠
    setTimeout(() => {
      reasoningCollapsed.value = true
    }, 1000)
  }
}, { immediate: false })

// 简化的渲染函数
const renderContent = async () => {
  try {
    if (asyncRenderer.value) {
      console.log('🎨 开始渲染消息:', props.message.id)
      const html = await asyncRenderer.value.renderMarkdown(props.message.content)
      renderedContent.value = html
      console.log('✅ 渲染完成:', props.message.id)

      nextTick(() => {
        setupCodeCopyHandlers()
      })
    } else {
      // 降级方案
      renderedContent.value = props.message.content.replace(/\n/g, '<br>')
      nextTick(() => {
        setupCodeCopyHandlers()
      })
    }
  } catch (error) {
    console.error('❌ 渲染失败:', error)
    // 显示原始内容
    renderedContent.value = props.message.content.replace(/\n/g, '<br>')
  }
}

// 监听消息对象变化（切换对话时）- 简化版本
watch(() => props.message, (newMessage, oldMessage) => {
  if (newMessage && newMessage.id !== oldMessage?.id) {
    console.log('🔄 消息对象变化:', oldMessage?.id, '->', newMessage.id)

    // 清空之前的渲染内容
    renderedContent.value = ''

    // 立即渲染新消息
    renderContent()
  }
}, { immediate: false })

// 组件卸载时清理
onUnmounted(() => {
  if (contentUpdateTimeout) {
    clearTimeout(contentUpdateTimeout)
  }
})
</script>

<style scoped>
.msg {
  display: flex;
  gap: 8px;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.msg:hover {
  background: #fafafa;
  margin: 0 -8px;
  padding: 8px;
  border-radius: 4px;
}

.avatar {
  width: 28px;
  height: 28px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.avatar svg {
  width: 16px;
  height: 16px;
}

.avatar-user { background: #3b82f6; color: white; }
.avatar-assistant { background: #10b981; color: white; }
.avatar-system { background: #f59e0b; color: white; }

.content {
  flex: 1;
  min-width: 0;
}

.header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
  font-size: 12px;
}

.role {
  font-weight: 600;
  color: #6b7280;
  text-transform: uppercase;
}

.time {
  color: #9ca3af;
  font-size: 11px;
}

.actions {
  margin-left: auto;
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s;
}

.msg:hover .actions {
  opacity: 1;
}

.btn {
  width: 24px;
  height: 24px;
  border: none;
  background: #f3f4f6;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.15s;
}

.btn:hover {
  background: #e5e7eb;
}

.btn svg {
  width: 14px;
  height: 14px;
  color: #6b7280;
}

.text {
  font-size: 14px;
  line-height: 1.5;
  color: #1f2937;
  word-wrap: break-word;
}

.status {
  margin-top: 4px;
  font-size: 12px;
  color: #6b7280;
}

.cursor {
  animation: blink 1s infinite;
  color: #10b981;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

/* Markdown 样式 */
.text :deep(p) {
  margin: 4px 0;
  line-height: 1.4;
}

.text :deep(h1),
.text :deep(h2),
.text :deep(h3),
.text :deep(h4) {
  margin: 8px 0 4px 0;
  font-weight: 600;
  line-height: 1.2;
}

.text :deep(h1) { font-size: 1.3em; color: #1f2937; border-bottom: 1px solid #e5e7eb; padding-bottom: 4px; }
.text :deep(h2) { font-size: 1.2em; color: #374151; }
.text :deep(h3) { font-size: 1.1em; color: #4b5563; }
.text :deep(h4) { font-size: 1.05em; color: #6b7280; }

.text :deep(hr) {
  border: none;
  border-top: 1px solid #e5e7eb;
  margin: 12px 0;
  opacity: 0.6;
}

.text :deep(ul) {
  margin: 6px 0;
  padding-left: 20px;
}

.text :deep(li) {
  margin: 2px 0;
  line-height: 1.4;
}

.text :deep(br) {
  line-height: 1.2;
}

.text :deep(strong) {
  font-weight: 600;
  color: #1f2937;
}

.text :deep(em) {
  font-style: italic;
  color: #6366f1;
}

.text :deep(code) {
  background: #f1f5f9;
  color: #dc2626;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'SF Mono', Monaco, Consolas, monospace;
  font-size: 0.9em;
}

.text :deep(a) {
  color: #3b82f6;
  text-decoration: none;
  border-bottom: 1px solid transparent;
  transition: border-color 0.15s;
}

.text :deep(a:hover) {
  border-bottom-color: #3b82f6;
}

.text :deep(ul) {
  margin: 8px 0;
  padding-left: 16px;
}

.text :deep(li) {
  margin: 2px 0;
  line-height: 1.4;
}

/* 代码块样式 */
.text :deep(.code-block) {
  margin: 6px 0;
  border-radius: 4px;
  overflow: hidden;
  border: 1px solid #e5e7eb;
  background: #fafbfc;
}

.text :deep(.code-header) {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 8px;
  background: #f8fafc;
  border-bottom: 1px solid #e5e7eb;
  font-size: 10px;
  min-height: 24px;
}

/* 分块渲染样式 */
.message-content {
  width: 100%;
}

.chunked-content {
  position: relative;
}

.load-more-content {
  margin-top: 12px;
  padding: 8px 0;
  text-align: center;
  border-top: 1px solid #f0f0f0;
}

.load-more-btn {
  font-size: 12px;
  height: 28px;
  padding: 0 12px;
  border-radius: 14px;
  transition: all 0.2s ease;
}

.load-more-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.text :deep(.code-lang) {
  color: #6b7280;
  font-weight: 600;
  font-size: 9px;
  text-transform: uppercase;
  background: #e5e7eb;
  padding: 1px 4px;
  border-radius: 2px;
}

.text :deep(.copy-code-btn) {
  display: flex;
  align-items: center;
  gap: 2px;
  background: #ffffff;
  border: 1px solid #d1d5db;
  color: #6b7280;
  cursor: pointer;
  padding: 2px 4px;
  border-radius: 2px;
  font-size: 9px;
  transition: all 0.15s;
  height: 18px;
}

.text :deep(.copy-code-btn:hover) {
  background: #f9fafb;
  border-color: #9ca3af;
}

.text :deep(.copy-code-btn.copied) {
  background: #dcfce7;
  border-color: #16a34a;
  color: #16a34a;
}

/* 思考过程样式 */
.reasoning-section {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  margin-bottom: 16px;
  overflow: hidden;
}

.reasoning-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  background: #f1f5f9;
  border-bottom: 1px solid #e2e8f0;
  color: #475569;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  user-select: none;
  min-height: 32px;
  transition: background-color 0.15s ease;
}

.reasoning-header:hover {
  background: #e2e8f0;
}

.reasoning-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.reasoning-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.reasoning-icon {
  font-size: 14px;
  color: #f59e0b;
}

.reasoning-title {
  font-weight: 500;
}

.reasoning-status {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 11px;
  opacity: 0.8;
}

.reasoning-stats {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 10px;
  opacity: 0.7;
  background: #e2e8f0;
  padding: 2px 6px;
  border-radius: 4px;
}

.stat-item {
  font-weight: 500;
}

.stat-separator {
  opacity: 0.6;
}

.copy-reasoning-btn {
  --n-color-hover: #e2e8f0 !important;
  --n-color-pressed: #cbd5e1 !important;
  color: #64748b !important;
}

.reasoning-toggle {
  display: flex;
  align-items: center;
}

.toggle-icon {
  font-size: 12px;
  opacity: 0.7;
}

.reasoning-status {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #64748b;
}

.reasoning-content {
  padding: 0;
  background: #fafbfc;
}

.reasoning-viewport {
  height: 96px; /* 6行 × 16px行高 */
  overflow: hidden;
  position: relative;
  background: white;
  transition: height 0.3s ease;
}

.reasoning-viewport.expanded {
  height: auto;
  max-height: 400px;
  overflow-y: auto;
}

.reasoning-text {
  font-size: 12px;
  line-height: 16px;
  color: #374151;
  font-family: 'SF Mono', 'Monaco', 'Consolas', 'Courier New', monospace;
  white-space: pre-wrap;
  word-wrap: break-word;
  padding: 8px 12px;
  height: 100%;
  overflow-y: auto;
  scroll-behavior: smooth;
}

.reasoning-expand-btn {
  padding: 4px 12px;
  background: #f8fafc;
  border-top: 1px solid #e2e8f0;
  text-align: center;
}

.expand-toggle {
  font-size: 11px;
  color: #64748b;
}

/* 自定义滚动条 */
.reasoning-text::-webkit-scrollbar,
.reasoning-viewport.expanded::-webkit-scrollbar {
  width: 4px;
}

.reasoning-text::-webkit-scrollbar-track,
.reasoning-viewport.expanded::-webkit-scrollbar-track {
  background: #f1f5f9;
}

.reasoning-text::-webkit-scrollbar-thumb,
.reasoning-viewport.expanded::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 2px;
}

.reasoning-text::-webkit-scrollbar-thumb:hover,
.reasoning-viewport.expanded::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

.thinking-indicator {
  color: #94a3b8;
  font-style: italic;
  margin-left: 8px;
}

.thinking-dots::after {
  content: '...';
  animation: thinking 1.5s infinite;
}

@keyframes thinking {
  0%, 20% { content: '...'; }
  40% { content: '....'; }
  60% { content: '.....'; }
  80%, 100% { content: '...'; }
}

.main-content {
  /* 主要内容区域，保持原有样式 */
}

.text :deep(.copy-code-btn svg) {
  width: 8px;
  height: 8px;
}

.text :deep(pre) {
  margin: 0;
  padding: 6px 8px;
  overflow-x: auto;
  font-family: 'SF Mono', Monaco, Consolas, monospace;
  font-size: 11px;
  line-height: 1.4;
  background: #ffffff;
  color: #1f2937;
}

.text :deep(pre code) {
  background: none;
  padding: 0;
  border-radius: 0;
  color: inherit;
  font-size: inherit;
}


</style>
