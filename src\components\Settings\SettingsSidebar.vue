<template>
  <div 
    class="settings-sidebar-overlay"
    :class="{ active: showSidebar }"
    @click="closeSidebar"
  >
    <div 
      class="settings-sidebar"
      :class="{ active: showSidebar }"
      @click.stop
    >
      <!-- 侧边栏头部 -->
      <div class="sidebar-header">
        <div class="header-content">
          <div class="header-left">
            <i class="i-carbon-settings text-xl" />
            <h2>设置</h2>
          </div>
          <button 
            class="close-button"
            @click="closeSidebar"
            title="关闭设置"
          >
            <i class="i-carbon-close" />
          </button>
        </div>
      </div>

      <!-- 设置导航 -->
      <div class="sidebar-nav">
        <div
          v-for="tab in settingsTabs"
          :key="tab.key"
          class="nav-item"
          :class="{ active: activeTab === tab.key }"
          @click="activeTab = tab.key"
        >
          <i :class="tab.icon" />
          <span>{{ tab.label }}</span>
        </div>
      </div>

      <!-- 设置内容区域 -->
      <div class="sidebar-content">
        <div class="content-header">
          <h3>{{ currentTabInfo?.label }}</h3>
          <p>{{ currentTabInfo?.description }}</p>
        </div>

        <div class="content-body">
          <!-- 通用设置 -->
          <div v-if="activeTab === 'general'" class="setting-section">
            <GeneralSettings />
          </div>

          <!-- AI提供商设置 -->
          <div v-else-if="activeTab === 'providers'" class="setting-section">
            <AIProviderSettings />
          </div>

          <!-- 模型参数设置 -->
          <div v-else-if="activeTab === 'models'" class="setting-section">
            <ModelSettings />
          </div>

          <!-- 助手管理 -->
          <div v-else-if="activeTab === 'assistants'" class="setting-section">
            <AssistantSettings />
          </div>

          <!-- 高级设置 -->
          <div v-else-if="activeTab === 'advanced'" class="setting-section">
            <AdvancedSettings />
          </div>

          <!-- 关于 -->
          <div v-else-if="activeTab === 'about'" class="setting-section">
            <AboutSettings />
          </div>
        </div>
      </div>

      <!-- 底部操作区 -->
      <div class="sidebar-footer">
        <div class="footer-actions">
          <n-button @click="resetSettings" quaternary size="small">
            <template #icon>
              <i class="i-carbon-reset" />
            </template>
            重置所有设置
          </n-button>
          
          <div class="footer-right">
            <n-button @click="closeSidebar" size="small">
              取消
            </n-button>
            <n-button @click="saveSettings" type="primary" size="small">
              <template #icon>
                <i class="i-carbon-checkmark" />
              </template>
              保存
            </n-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useMessage } from 'naive-ui'
import { useSettingsStore } from '@/stores/settings'

// 导入设置组件
import AIProviderSettings from './AIProviderSettings.vue'
import GeneralSettings from './GeneralSettings.vue'
import ModelSettings from './ModelSettings.vue'
import AssistantSettings from './AssistantSettings.vue'
import AdvancedSettings from './AdvancedSettings.vue'
import AboutSettings from './AboutSettings.vue'

const message = useMessage()
const settingsStore = useSettingsStore()

// 响应式数据
const showSidebar = ref(false)
const activeTab = ref('providers') // 默认显示AI提供商设置

// 设置标签页配置
const settingsTabs = [
  {
    key: 'general',
    label: '通用',
    icon: 'i-carbon-settings',
    description: '应用外观、语言和基本行为设置'
  },
  {
    key: 'providers',
    label: 'AI提供商',
    icon: 'i-carbon-cloud-service-management',
    description: '配置和管理AI服务提供商'
  },
  {
    key: 'models',
    label: '模型参数',
    icon: 'i-carbon-model-alt',
    description: '调整AI模型的参数和行为'
  },
  {
    key: 'assistants',
    label: '助手管理',
    icon: 'i-carbon-user-multiple',
    description: '创建和管理AI助手'
  },
  {
    key: 'advanced',
    label: '高级设置',
    icon: 'i-carbon-settings-adjust',
    description: '高级功能和开发者选项'
  },
  {
    key: 'about',
    label: '关于',
    icon: 'i-carbon-information',
    description: '应用信息和版本详情'
  }
]

// 计算属性
const currentTabInfo = computed(() => settingsTabs.find(tab => tab.key === activeTab.value))

// 方法
const openSidebar = () => {
  showSidebar.value = true
  // 添加body类防止背景滚动
  document.body.classList.add('settings-sidebar-open')
}

const closeSidebar = () => {
  showSidebar.value = false
  // 移除body类恢复背景滚动
  document.body.classList.remove('settings-sidebar-open')
}

const saveSettings = () => {
  try {
    settingsStore.saveSettings()
    message.success('设置已保存')
    setTimeout(() => {
      closeSidebar()
    }, 1000)
  } catch (error) {
    message.error('保存设置失败')
    console.error('保存设置失败:', error)
  }
}

const resetSettings = () => {
  message.warning('重置功能开发中...')
}

// 监听全局事件
const handleOpenSettings = () => {
  openSidebar()
}

// 监听ESC键关闭侧边栏
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Escape' && showSidebar.value) {
    closeSidebar()
  }
}

// 组件挂载和卸载
onMounted(() => {
  window.addEventListener('open-settings-sidebar', handleOpenSettings)
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  window.removeEventListener('open-settings-sidebar', handleOpenSettings)
  document.removeEventListener('keydown', handleKeydown)
  document.body.classList.remove('settings-sidebar-open')
})

// 暴露方法给父组件
defineExpose({
  openSidebar,
  closeSidebar
})
</script>

<style scoped>
/* 遮罩层 */
.settings-sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.settings-sidebar-overlay.active {
  opacity: 1;
  visibility: visible;
}

/* 侧边栏主体 */
.settings-sidebar {
  position: fixed;
  top: 0;
  right: 0;
  width: 480px;
  height: 100vh;
  background: #ffffff;
  box-shadow: -4px 0 20px rgba(0, 0, 0, 0.15);
  transform: translateX(100%);
  transition: transform 0.3s ease;
  display: flex;
  flex-direction: column;
  z-index: 1001;
}

.settings-sidebar.active {
  transform: translateX(0);
}

/* 侧边栏头部 */
.sidebar-header {
  height: 64px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  flex-shrink: 0;
}

.header-content {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-left h2 {
  font-size: 18px;
  font-weight: 600;
  margin: 0;
}

.close-button {
  width: 32px;
  height: 32px;
  border: none;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.close-button:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* 导航区域 */
.sidebar-nav {
  background: #f8fafc;
  border-bottom: 1px solid #e5e7eb;
  padding: 16px 0;
  flex-shrink: 0;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 24px;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #6b7280;
  font-size: 14px;
  border-left: 3px solid transparent;
}

.nav-item:hover {
  background: #f1f5f9;
  color: #374151;
}

.nav-item.active {
  background: #eff6ff;
  color: #2563eb;
  font-weight: 500;
  border-left-color: #2563eb;
}

.nav-item i {
  font-size: 16px;
}

/* 内容区域 */
.sidebar-content {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.content-header {
  padding: 24px 24px 16px;
  background: white;
  border-bottom: 1px solid #f1f5f9;
  flex-shrink: 0;
}

.content-header h3 {
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.content-header p {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
}

.content-body {
  flex: 1;
  overflow-y: auto;
  padding: 0 24px 24px;
}

.setting-section {
  background: white;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  overflow: hidden;
  margin-top: 16px;
}

/* 底部操作区 */
.sidebar-footer {
  height: 72px;
  background: white;
  border-top: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  padding: 0 24px;
  flex-shrink: 0;
}

.footer-actions {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.footer-right {
  display: flex;
  gap: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .settings-sidebar {
    width: 100vw;
  }
}

@media (max-width: 480px) {
  .sidebar-nav {
    padding: 12px 0;
  }
  
  .nav-item {
    padding: 10px 16px;
  }
  
  .content-header {
    padding: 16px 16px 12px;
  }
  
  .content-body {
    padding: 0 16px 16px;
  }
  
  .sidebar-footer {
    padding: 0 16px;
  }
}
</style>

<style>
/* 全局样式：防止背景滚动 */
body.settings-sidebar-open {
  overflow: hidden;
}
</style>
