<template>
  <div class="ai-provider-settings">
    <!-- 左侧提供商列表 -->
    <div class="provider-sidebar">
      <div class="sidebar-header">
        <div class="header-content">
          <h3>AI提供商</h3>
          <span class="provider-subtitle">已启用 {{ enabledCount }}/{{ providers.length }}</span>
        </div>
      </div>

      <div class="provider-list">
        <div
          v-for="provider in providers"
          :key="provider.id"
          class="provider-item"
          :class="{
            'provider-item--active': selectedProvider?.id === provider.id,
            'provider-item--enabled': provider.enabled
          }"
          @click="selectProvider(provider)"
        >
          <div class="provider-info">
            <div class="provider-icon">{{ provider.icon || '🤖' }}</div>
            <div class="provider-details">
              <h4 class="provider-name">{{ provider.displayName }}</h4>
              <p class="provider-description">
                {{ getProviderDescription(provider.id) }}
              </p>
            </div>
          </div>

          <div class="provider-toggle">
            <n-switch
              :value="provider.enabled"
              @update:value="(value) => toggleProvider(provider.id, value)"
              @click.stop
              size="small"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 右侧配置面板 -->
    <div class="provider-config">
      <AIProviderDetail
        v-if="selectedProvider"
        :provider="selectedProvider"
        @update="handleProviderUpdate"
        @test-connection="handleTestConnection"
      />
      <div v-else class="no-selection">
        <div class="no-selection-content">
          <i class="i-carbon-settings text-4xl text-gray-400" />
          <h3>选择AI提供商</h3>
          <p>请从左侧列表中选择一个AI提供商来配置其设置</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useMessage } from 'naive-ui'
import { useSettingsStore } from '@/stores/settings'
import AIProviderDetail from './AIProviderDetail.vue'
import type { AIProvider } from '@/types'

const message = useMessage()
const settingsStore = useSettingsStore()

// 响应式数据
const selectedProvider = ref<AIProvider | null>(null)

// 计算属性
const providers = computed(() => settingsStore.providers)
const enabledCount = computed(() => providers.value.filter(p => p.enabled).length)

// 提供商描述映射
const providerDescriptions = {
  'openai': 'OpenAI - GPT系列模型，支持多种AI任务',
  'anthropic': 'Anthropic - Claude系列模型，注重安全和有用性',
  'deepseek': 'DeepSeek AI - 深度求索，支持多种专业AI能力',
  'google': 'Google Gemini - 谷歌多模态AI模型'
}

// 方法
const getProviderDescription = (providerId: string) => {
  return providerDescriptions[providerId] || '专业的AI服务提供商'
}

const selectProvider = (provider: AIProvider) => {
  selectedProvider.value = provider
}

const toggleProvider = (providerId: string, enabled: boolean) => {
  settingsStore.updateProvider(providerId, { enabled })
  if (enabled) {
    message.success(`已启用 ${providers.value.find(p => p.id === providerId)?.displayName}`)
  } else {
    message.info(`已禁用 ${providers.value.find(p => p.id === providerId)?.displayName}`)
  }
}

const handleProviderUpdate = (providerId: string, updates: Partial<AIProvider>) => {
  // 处理提供商更新事件
  console.log('Provider updated:', providerId, updates)
}

const handleTestConnection = (providerId: string, success: boolean) => {
  // 处理连接测试事件
  console.log('Connection test:', providerId, success ? 'success' : 'failed')
}

// 初始化选择第一个提供商
if (providers.value.length > 0) {
  selectProvider(providers.value[0])
}
</script>

<style scoped>
.ai-provider-settings {
  display: flex;
  height: 100%;
  min-height: 500px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* 左侧边栏 */
.provider-sidebar {
  flex: 0 0 200px;
  background: #f8fafc;
  border-right: 1px solid #e5e7eb;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.sidebar-header {
  padding: 8px 12px;
  border-bottom: 1px solid #e5e7eb;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.sidebar-header h3 {
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  margin: 0;
}

.provider-subtitle {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
}

.provider-list {
  flex: 1;
  overflow-y: auto;
  padding: 2px 4px;
}

.provider-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 4px 8px;
  border-radius: 3px;
  margin-bottom: 1px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.provider-item:hover {
  background: #f1f5f9;
}

.provider-item--active {
  background: #eff6ff;
  color: #2563eb;
}

.provider-item--enabled {
  border-left: 2px solid #10b981;
  padding-left: 8px;
}

/* 右侧配置区域 */
.provider-config {
  flex: 1;
  overflow-y: auto;
  background: white;
  padding: 16px;
}

.provider-info {
  display: flex;
  align-items: center;
  gap: 4px;
  flex: 1;
}

.provider-icon {
  font-size: 13px;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f3f4f6;
  border-radius: 2px;
  flex-shrink: 0;
}

.provider-details {
  flex: 1;
  min-width: 0;
}

.provider-name {
  font-size: 12px;
  font-weight: 500;
  color: #1f2937;
  margin-bottom: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.2;
}

.provider-description {
  font-size: 10px;
  color: #9ca3af;
  line-height: 1.1;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.provider-toggle {
  flex-shrink: 0;
}

/* 无选择状态 */
.no-selection {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 300px;
}

.no-selection-content {
  text-align: center;
  color: #6b7280;
}

.no-selection-content h3 {
  font-size: 16px;
  font-weight: 500;
  margin: 16px 0 8px 0;
  color: #374151;
}

.no-selection-content p {
  font-size: 14px;
  margin: 0;
}

/* 提供商配置面板由AIProviderDetail组件处理 */

/* 响应式设计 */
@media (max-width: 1024px) {
  .provider-sidebar {
    flex: 0 0 200px;
  }

  .provider-config {
    padding: 16px;
  }
}

@media (max-width: 768px) {
  .provider-sidebar {
    flex: 0 0 180px;
  }

  .sidebar-header {
    padding: 12px;
  }

  .sidebar-header h3 {
    font-size: 15px;
  }

  .provider-subtitle {
    font-size: 13px;
  }

  .provider-item {
    padding: 8px 10px;
  }

  .provider-name {
    font-size: 13px;
  }

  .provider-description {
    font-size: 11px;
  }

  .provider-config {
    padding: 12px;
  }
}
</style>
