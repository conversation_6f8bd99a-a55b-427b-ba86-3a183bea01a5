<template>
  <div id="app" class="h-full">
    <n-config-provider :theme="theme" :theme-overrides="themeOverrides">
      <n-global-style />
      <n-message-provider>
        <n-notification-provider>
          <n-dialog-provider>
            <AppLayout />
          </n-dialog-provider>
        </n-notification-provider>
      </n-message-provider>
    </n-config-provider>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { darkTheme, type GlobalThemeOverrides } from 'naive-ui'
import { useSettingsStore } from '@/stores/settings'
import AppLayout from '@/components/Layout/AppLayout.vue'

const settingsStore = useSettingsStore()

// 主题配置
const theme = computed(() => {
  const appTheme = settingsStore.app.theme
  if (appTheme === 'dark') return darkTheme
  if (appTheme === 'light') return null
  
  // 自动模式：根据系统主题
  return window.matchMedia('(prefers-color-scheme: dark)').matches ? darkTheme : null
})

// 主题覆盖配置
const themeOverrides: GlobalThemeOverrides = {
  common: {
    primaryColor: '#3b82f6',
    primaryColorHover: '#2563eb',
    primaryColorPressed: '#1d4ed8',
    primaryColorSuppl: '#60a5fa',
    borderRadius: '8px',
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif'
  },
  Card: {
    borderRadius: '12px',
    paddingMedium: '16px'
  },
  Button: {
    borderRadius: '8px',
    paddingMedium: '0 16px',
    heightMedium: '36px'
  },
  Input: {
    borderRadius: '8px',
    paddingMedium: '0 12px',
    heightMedium: '36px'
  },
  Layout: {
    siderColor: '#fafafa',
    headerColor: '#ffffff',
    headerBorderColor: '#e5e7eb'
  },
  Scrollbar: {
    color: 'rgba(0, 0, 0, 0.25)',
    colorHover: 'rgba(0, 0, 0, 0.4)'
  }
}
</script>

<style>
/* 全局样式已在 main.css 中定义 */
</style>
