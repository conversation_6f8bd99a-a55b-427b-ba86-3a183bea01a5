// Markdown渲染Web Worker
// 使用marked.js库进行解析

// 动态导入marked.js
let marked = null

// 初始化marked
const initMarked = async () => {
  if (!marked) {
    try {
      // 从CDN加载marked
      const markedModule = await import('https://cdn.jsdelivr.net/npm/marked@9.1.6/+esm')
      marked = markedModule.marked

      // 配置marked选项
      marked.use({
        async: true,        // 支持异步和流式
        breaks: true,       // 换行转<br>
        gfm: true,         // GitHub风格Markdown
        pedantic: false,   // 不严格遵循原始markdown.pl
        silent: false      // 显示错误信息
      })

      return true
    } catch (error) {
      return false
    }
  }
  return true
}

// 渲染缓存 - 多线程优化
const renderCache = new Map()
const MAX_CACHE_SIZE = 200
const CACHE_EXPIRE_TIME = 5 * 60 * 1000 // 5分钟过期

// 清理过期缓存
const cleanExpiredCache = () => {
  const now = Date.now()
  for (const [key, value] of renderCache.entries()) {
    if (now - value.timestamp > CACHE_EXPIRE_TIME) {
      renderCache.delete(key)
    }
  }
}

// 定期清理缓存
setInterval(cleanExpiredCache, 60000) // 每分钟清理一次

// 获取缓存键
const getCacheKey = (content, options = {}) => {
  return `${content.length}-${JSON.stringify(options)}-${content.slice(0, 100)}`
}

// Markdown渲染器 - 带缓存优化
const renderMarkdown = async (content, options = {}) => {
  // 检查缓存
  const cacheKey = getCacheKey(content, options)
  const cached = renderCache.get(cacheKey)

  if (cached && Date.now() - cached.timestamp < CACHE_EXPIRE_TIME) {
    return cached.result
  }
  const markedReady = await initMarked()

  if (!markedReady || !marked) {
    // 降级到简单解析器
    return simpleMarkdownRender(content)
  }

  try {
    // 使用marked.parse进行解析
    let html = await marked.parse(content)

    // 后处理：添加代码复制按钮
    html = html.replace(/<pre><code class="language-(\w+)">([\s\S]*?)<\/code><\/pre>/g, (match, lang, code) => {
      const decodedCode = code
        .replace(/&lt;/g, '<')
        .replace(/&gt;/g, '>')
        .replace(/&amp;/g, '&')
        .replace(/&quot;/g, '"')
        .replace(/&#39;/g, "'")

      return `<div class="code-block">
        <div class="code-header">
          <span class="code-lang">${lang.toUpperCase()}</span>
          <button class="copy-code-btn" data-code="${encodeURIComponent(decodedCode)}">
            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
              <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
            </svg>
            <span class="copy-text">复制</span>
          </button>
        </div>
        <pre><code class="language-${lang}">${code}</code></pre>
      </div>`
    })

    // 处理没有语言标识的代码块
    html = html.replace(/<pre><code>([\s\S]*?)<\/code><\/pre>/g, (match, code) => {
      const decodedCode = code
        .replace(/&lt;/g, '<')
        .replace(/&gt;/g, '>')
        .replace(/&amp;/g, '&')
        .replace(/&quot;/g, '"')
        .replace(/&#39;/g, "'")

      return `<div class="code-block">
        <div class="code-header">
          <span class="code-lang">TEXT</span>
          <button class="copy-code-btn" data-code="${encodeURIComponent(decodedCode)}">
            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
              <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2 2v1"></path>
            </svg>
            <span class="copy-text">复制</span>
          </button>
        </div>
        <pre><code>${code}</code></pre>
      </div>`
    })

    // 保存到缓存
    if (renderCache.size >= MAX_CACHE_SIZE) {
      // 清理最旧的缓存项
      const oldestKey = renderCache.keys().next().value
      renderCache.delete(oldestKey)
    }

    renderCache.set(cacheKey, {
      result: html,
      timestamp: Date.now()
    })

    return html
  } catch (error) {
    const fallbackResult = simpleMarkdownRender(content)

    // 即使是降级结果也缓存起来
    renderCache.set(cacheKey, {
      result: fallbackResult,
      timestamp: Date.now()
    })

    return fallbackResult
  }
}

// 简化的降级解析器
const simpleMarkdownRender = (content) => {
  let html = content
  
  // 1. 处理代码块
  html = html.replace(/```(\w+)?\n([\s\S]*?)```/g, (_, lang, code) => {
    const language = lang || 'text'
    const codeId = `code-${Math.random().toString(36).substring(2, 11)}`
    const trimmedCode = code.trim()
    return `<div class="code-block">
      <div class="code-header">
        <span class="code-lang">${language.toUpperCase()}</span>
        <button class="copy-code-btn" data-code="${encodeURIComponent(trimmedCode)}" data-code-id="${codeId}" title="复制代码">
          <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
            <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
          </svg>
          <span class="copy-text">复制</span>
        </button>
      </div>
      <pre><code class="language-${language}" id="${codeId}">${escapeHtml(trimmedCode)}</code></pre>
    </div>`
  })
  
  // 2. 处理标题
  html = html.replace(/^(#{1,6})\s+(.+)$/gm, (match, hashes, text) => {
    const level = hashes.length
    return `<h${level}>${text}</h${level}>`
  })
  
  // 3. 处理列表
  // 无序列表
  html = html.replace(/^\s*[-*+]\s+(.+)$/gm, '<li>$1</li>')
  // 有序列表（数字列表）
  html = html.replace(/^\s*\d+\.\s+(.+)$/gm, '<li>$1</li>')
  // 包装连续的li为ul
  html = html.replace(/(<li>[\s\S]*?<\/li>(?:\s*<li>[\s\S]*?<\/li>)*)/g, '<ul>$1</ul>')
  
  // 4. 处理引用
  html = html.replace(/^>\s+(.+)$/gm, '<blockquote>$1</blockquote>')

  // 5. 处理水平分隔线
  html = html.replace(/^---+$/gm, '<hr>')
  html = html.replace(/^\*\*\*+$/gm, '<hr>')
  html = html.replace(/^___+$/gm, '<hr>')

  // 6. 处理行内格式
  html = html
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    .replace(/\*(.*?)\*/g, '<em>$1</em>')
    .replace(/`(.*?)`/g, '<code>$1</code>')
    .replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank">$1</a>')
  
  // 7. 处理换行 - 避免过多空行
  html = html.replace(/\n\n+/g, '<br><br>')  // 多个换行变成两个br
  html = html.replace(/\n/g, '<br>')         // 单个换行变成一个br

  return html
}

// HTML转义函数
const escapeHtml = (text) => {
  return text
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#39;')
}

// 性能监控
let taskCount = 0
let totalProcessingTime = 0
let errorCount = 0

// 处理消息 - 支持异步渲染和性能监控
self.onmessage = async function(e) {
  const taskId = ++taskCount
  const startTime = performance.now()

  try {
    const { id, content, type, options = {} } = e.data

    // 输入验证
    if (!id || !content || !type) {
      throw new Error('缺少必要参数: id, content, type')
    }

    // 内容长度检查
    if (content.length > 100000) { // 100KB限制
      // 内容过长，可能影响性能
    }

    let result
    let renderMethod = 'unknown'

    switch (type) {
      case 'markdown':
        // 使用Marked.js渲染器
        result = await renderMarkdown(content)
        renderMethod = 'marked.js'
        break

      case 'highlight':
        // 简单的代码高亮
        result = `<pre><code class="language-${options.language || 'text'}">${escapeHtml(content)}</code></pre>`
        renderMethod = 'simple-highlight'
        break





      default:
        throw new Error(`未知的渲染类型: ${type}`)
    }

    // 计算处理时间
    const processingTime = performance.now() - startTime
    totalProcessingTime += processingTime

    // 性能统计
    const avgProcessingTime = totalProcessingTime / taskCount
    const throughput = content.length / processingTime * 1000 // 字符/秒

    // 返回结果
    self.postMessage({
      id,
      result,
      success: true,
      stats: {
        taskId,
        processingTime: Math.round(processingTime * 100) / 100,
        avgProcessingTime: Math.round(avgProcessingTime * 100) / 100,
        throughput: Math.round(throughput),
        contentLength: content.length,
        renderMethod,
        taskCount,
        errorCount
      }
    })

    // 任务完成

  } catch (error) {
    errorCount++
    const processingTime = performance.now() - startTime

    self.postMessage({
      id: e.data.id || 'unknown',
      error: error.message || '未知错误',
      success: false,
      stats: {
        taskId,
        processingTime: Math.round(processingTime * 100) / 100,
        errorCount,
        taskCount
      }
    })
  }
}



// Worker状态报告
setInterval(() => {
  if (taskCount > 0) {
    const avgTime = totalProcessingTime / taskCount
    // Worker状态统计
  }
}, 30000) // 每30秒报告一次
