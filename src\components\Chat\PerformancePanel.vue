<template>
  <div class="performance-panel" :class="{ 'panel-expanded': expanded }">
    <!-- 面板头部 -->
    <div class="panel-header" @click="togglePanel">
      <div class="header-left">
        <n-icon :component="ActivityIcon" class="panel-icon" />
        <span class="panel-title">性能监控</span>
        <n-badge 
          :value="stats.taskCount" 
          :max="999"
          type="info"
          size="small"
          v-if="stats.taskCount > 0"
        />
      </div>
      <div class="header-right">
        <span class="status-indicator" :class="statusClass">{{ statusText }}</span>
        <n-icon 
          :component="expanded ? ChevronUpIcon : ChevronDownIcon" 
          class="expand-icon"
        />
      </div>
    </div>

    <!-- 面板内容 -->
    <div class="panel-content" v-show="expanded">
      <!-- 实时性能指标 -->
      <div class="metrics-section">
        <h4 class="section-title">实时性能</h4>
        <div class="metrics-grid">
          <div class="metric-item">
            <span class="metric-label">内存使用</span>
            <span class="metric-value">{{ memoryUsage }}MB</span>
            <div class="metric-bar">
              <div 
                class="metric-fill" 
                :style="{ width: `${memoryUsagePercent}%` }"
              />
            </div>
          </div>
          
          <div class="metric-item">
            <span class="metric-label">FPS</span>
            <span class="metric-value">{{ fps }}</span>
            <div class="metric-bar">
              <div 
                class="metric-fill fps-fill" 
                :style="{ width: `${Math.min(fps / 60 * 100, 100)}%` }"
              />
            </div>
          </div>
          
          <div class="metric-item">
            <span class="metric-label">渲染时间</span>
            <span class="metric-value">{{ avgRenderTime }}ms</span>
          </div>
          
          <div class="metric-item">
            <span class="metric-label">消息数量</span>
            <span class="metric-value">{{ messageCount }}</span>
          </div>
        </div>
      </div>

      <!-- Worker状态 -->
      <div class="worker-section">
        <h4 class="section-title">Worker状态</h4>
        <div class="worker-stats">
          <div class="stat-row">
            <span class="stat-label">状态:</span>
            <span class="stat-value" :class="workerStatusClass">{{ workerStatus }}</span>
          </div>
          <div class="stat-row">
            <span class="stat-label">待处理任务:</span>
            <span class="stat-value">{{ stats.pendingTasks }}</span>
          </div>
          <div class="stat-row">
            <span class="stat-label">已完成任务:</span>
            <span class="stat-value">{{ stats.taskCount }}</span>
          </div>
          <div class="stat-row">
            <span class="stat-label">错误数量:</span>
            <span class="stat-value error">{{ stats.errorCount }}</span>
          </div>
        </div>
      </div>

      <!-- 缓存统计 -->
      <div class="cache-section">
        <h4 class="section-title">缓存统计</h4>
        <div class="cache-stats">
          <div class="stat-row">
            <span class="stat-label">缓存大小:</span>
            <span class="stat-value">{{ cacheStats.size }}/{{ cacheStats.maxSize }}</span>
          </div>
          <div class="stat-row">
            <span class="stat-label">命中率:</span>
            <span class="stat-value">{{ cacheHitRate }}%</span>
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="actions-section">
        <n-space>
          <n-button size="small" @click="clearCache" secondary>
            清理缓存
          </n-button>
          <n-button size="small" @click="resetStats" secondary>
            重置统计
          </n-button>
          <n-button size="small" @click="exportStats" secondary>
            导出数据
          </n-button>
        </n-space>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { 
  Activity as ActivityIcon,
  ChevronUp as ChevronUpIcon,
  ChevronDown as ChevronDownIcon
} from 'lucide-vue-next'
import { monitorMemoryUsage, PerformanceMonitor } from '@/utils/performance'

interface Props {
  messageCount?: number
  asyncRenderer?: any
  virtualScroll?: any
}

const props = withDefaults(defineProps<Props>(), {
  messageCount: 0
})

const emit = defineEmits<{
  clearCache: []
  resetStats: []
}>()

// 响应式数据
const expanded = ref(false)
const memoryUsage = ref(0)
const memoryUsagePercent = ref(0)
const fps = ref(60)
const avgRenderTime = ref(0)
const performanceMonitor = new PerformanceMonitor()

// 性能统计
const stats = ref({
  taskCount: 0,
  pendingTasks: 0,
  errorCount: 0
})

// 缓存统计
const cacheStats = ref({
  size: 0,
  maxSize: 100
})

// 计算属性
const statusClass = computed(() => {
  if (memoryUsagePercent.value > 80) return 'status-critical'
  if (memoryUsagePercent.value > 60) return 'status-warning'
  return 'status-good'
})

const statusText = computed(() => {
  if (memoryUsagePercent.value > 80) return '性能告警'
  if (memoryUsagePercent.value > 60) return '性能一般'
  return '性能良好'
})

const workerStatus = computed(() => {
  if (props.asyncRenderer?.isWorkerReady?.value) return '就绪'
  return '未就绪'
})

const workerStatusClass = computed(() => {
  return props.asyncRenderer?.isWorkerReady?.value ? 'status-ready' : 'status-not-ready'
})

const cacheHitRate = computed(() => {
  const total = cacheStats.value.size + stats.value.pendingTasks
  if (total === 0) return 0
  return Math.round((cacheStats.value.size / total) * 100)
})

// 方法
const togglePanel = () => {
  expanded.value = !expanded.value
}

const updateStats = () => {
  // 更新内存使用
  const memory = monitorMemoryUsage()
  if (memory) {
    memoryUsage.value = Math.round(memory.used / (1024 * 1024))
    memoryUsagePercent.value = Math.round(memory.usagePercent)
  }

  // 更新异步渲染器统计
  if (props.asyncRenderer) {
    const perfStats = props.asyncRenderer.getPerformanceStats?.()
    if (perfStats) {
      stats.value = {
        taskCount: perfStats.taskCount || 0,
        pendingTasks: perfStats.pendingTasks || 0,
        errorCount: perfStats.errorCount || 0
      }
    }

    const cacheStatsData = props.asyncRenderer.getCacheStats?.()
    if (cacheStatsData) {
      cacheStats.value = cacheStatsData
    }
  }

  // 更新FPS
  updateFPS()
}

let frameCount = 0
let lastTime = performance.now()

const updateFPS = () => {
  frameCount++
  const currentTime = performance.now()
  
  if (currentTime - lastTime >= 1000) {
    fps.value = Math.round(frameCount * 1000 / (currentTime - lastTime))
    frameCount = 0
    lastTime = currentTime
  }
  
  requestAnimationFrame(updateFPS)
}

const clearCache = () => {
  if (props.asyncRenderer?.clearCache) {
    props.asyncRenderer.clearCache()
  }
  emit('clearCache')
}

const resetStats = () => {
  stats.value = {
    taskCount: 0,
    pendingTasks: 0,
    errorCount: 0
  }
  performanceMonitor.clearMetrics()
  emit('resetStats')
}

const exportStats = () => {
  const data = {
    timestamp: new Date().toISOString(),
    memory: {
      usage: memoryUsage.value,
      usagePercent: memoryUsagePercent.value
    },
    performance: {
      fps: fps.value,
      avgRenderTime: avgRenderTime.value
    },
    worker: stats.value,
    cache: cacheStats.value,
    messages: props.messageCount
  }
  
  const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `performance-stats-${Date.now()}.json`
  a.click()
  URL.revokeObjectURL(url)
}

// 生命周期
let updateInterval: NodeJS.Timeout

onMounted(() => {
  updateFPS()
  updateInterval = setInterval(updateStats, 1000) // 每秒更新一次
})

onUnmounted(() => {
  if (updateInterval) {
    clearInterval(updateInterval)
  }
})
</script>

<style scoped>
.performance-panel {
  position: fixed;
  top: 20px;
  right: 20px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  min-width: 280px;
  max-width: 400px;
  transition: all 0.3s ease;
}

.panel-expanded {
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  cursor: pointer;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s ease;
}

.panel-header:hover {
  background-color: #f9fafb;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.panel-icon {
  color: #10b981;
}

.panel-title {
  font-weight: 600;
  font-size: 14px;
  color: #374151;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-indicator {
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: 500;
}

.status-good {
  background: #d1fae5;
  color: #065f46;
}

.status-warning {
  background: #fef3c7;
  color: #92400e;
}

.status-critical {
  background: #fee2e2;
  color: #991b1b;
}

.expand-icon {
  transition: transform 0.2s ease;
}

.panel-content {
  padding: 16px;
  max-height: 500px;
  overflow-y: auto;
}

.section-title {
  font-size: 13px;
  font-weight: 600;
  color: #374151;
  margin: 0 0 12px 0;
  padding-bottom: 4px;
  border-bottom: 1px solid #f0f0f0;
}

.metrics-section {
  margin-bottom: 20px;
}

.metrics-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.metric-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.metric-label {
  font-size: 11px;
  color: #6b7280;
  font-weight: 500;
}

.metric-value {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.metric-bar {
  height: 4px;
  background: #f3f4f6;
  border-radius: 2px;
  overflow: hidden;
}

.metric-fill {
  height: 100%;
  background: #10b981;
  transition: width 0.3s ease;
}

.fps-fill {
  background: #3b82f6;
}

.worker-section,
.cache-section {
  margin-bottom: 16px;
}

.worker-stats,
.cache-stats {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.stat-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
}

.stat-label {
  color: #6b7280;
}

.stat-value {
  font-weight: 500;
  color: #374151;
}

.stat-value.error {
  color: #dc2626;
}

.status-ready {
  color: #10b981;
}

.status-not-ready {
  color: #dc2626;
}

.actions-section {
  margin-top: 16px;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
}
</style>
