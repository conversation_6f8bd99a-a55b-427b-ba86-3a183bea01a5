<template>
  <div class="assistant-settings">
    <div class="settings-section">
      <div class="section-header">
        <h4>助手管理</h4>
        <p>管理和配置AI助手</p>
      </div>
      
      <div class="section-content">
        <div class="assistant-list">
          <div
            v-for="assistant in assistants"
            :key="assistant.id"
            class="assistant-item"
          >
            <div class="assistant-info">
              <div class="assistant-avatar">{{ assistant.avatar }}</div>
              <div class="assistant-details">
                <h5>{{ assistant.name }}</h5>
                <p>{{ assistant.description }}</p>
                <div class="assistant-tags">
                  <span
                    v-for="tag in assistant.tags"
                    :key="tag"
                    class="tag"
                  >
                    {{ tag }}
                  </span>
                </div>
              </div>
            </div>
            <div class="assistant-actions">
              <n-switch
                :value="assistant.isEnabled"
                @update:value="(value) => toggleAssistant(assistant.id, value)"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useMessage } from 'naive-ui'
import { useSettingsStore } from '@/stores/settings'

const message = useMessage()
const settingsStore = useSettingsStore()

// 计算属性
const assistants = computed(() => settingsStore.assistants)

// 方法
const toggleAssistant = (assistantId: string, enabled: boolean) => {
  settingsStore.updateAssistant(assistantId, { isEnabled: enabled })
  const assistant = assistants.value.find(a => a.id === assistantId)
  if (assistant) {
    message.success(`${assistant.name} 已${enabled ? '启用' : '禁用'}`)
  }
}
</script>

<style scoped>
.assistant-settings {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.settings-section {
  background: white;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  overflow: hidden;
}

.section-header {
  padding: 20px 24px;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

.section-header h4 {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 4px 0;
}

.section-header p {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
}

.section-content {
  padding: 24px;
}

.assistant-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.assistant-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.assistant-item:hover {
  border-color: #3b82f6;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
}

.assistant-info {
  display: flex;
  align-items: center;
  gap: 16px;
  flex: 1;
}

.assistant-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
}

.assistant-details h5 {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 4px 0;
}

.assistant-details p {
  font-size: 14px;
  color: #6b7280;
  margin: 0 0 8px 0;
}

.assistant-tags {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}

.tag {
  padding: 2px 8px;
  background: #eff6ff;
  color: #2563eb;
  border-radius: 12px;
  font-size: 12px;
}

.assistant-actions {
  flex: 0 0 auto;
}
</style>
