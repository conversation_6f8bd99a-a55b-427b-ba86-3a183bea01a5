// Prevents additional console window on Windows in release, DO NOT REMOVE!!
#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

use tauri::State;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Mutex;
use uuid::Uuid;
use chrono::{DateTime, Utc};

// 数据结构定义
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Message {
    pub id: String,
    pub conversation_id: String,
    pub role: String, // "user", "assistant", "system"
    pub content: String,
    pub timestamp: DateTime<Utc>,
    pub tokens: Option<u32>,
    pub model: Option<String>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ConversationGroup {
    pub id: String,
    pub name: String,
    pub color: Option<String>,
    pub icon: Option<String>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub conversation_count: u32,
    pub is_collapsed: Option<bool>,
    pub sort_order: u32,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct Conversation {
    pub id: String,
    pub title: String,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub message_count: u32,
    pub model: Option<String>,
    pub is_archived: bool,
    pub is_pinned: bool,
    pub group_id: Option<String>,
    pub tags: Option<Vec<String>>,
}

#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct ChatSettings {
    pub temperature: f32,
    pub max_tokens: u32,
    pub top_p: f32,
    pub frequency_penalty: f32,
    pub presence_penalty: f32,
    pub system_prompt: Option<String>,
}

// 应用状态
#[derive(Default)]
pub struct AppState {
    pub conversations: Mutex<HashMap<String, Conversation>>,
    pub conversation_groups: Mutex<HashMap<String, ConversationGroup>>,
    pub messages: Mutex<HashMap<String, Vec<Message>>>,
    pub settings: Mutex<ChatSettings>,
}

// Tauri 命令
#[tauri::command]
async fn create_conversation(
    title: String,
    state: State<'_, AppState>,
) -> Result<Conversation, String> {
    let conversation = Conversation {
        id: Uuid::new_v4().to_string(),
        title,
        created_at: Utc::now(),
        updated_at: Utc::now(),
        message_count: 0,
        model: Some("gpt-3.5-turbo".to_string()),
        is_archived: false,
        is_pinned: false,
        group_id: None,
        tags: None,
    };

    let mut conversations = state.conversations.lock().unwrap();
    conversations.insert(conversation.id.clone(), conversation.clone());

    Ok(conversation)
}

#[tauri::command]
async fn get_conversations(state: State<'_, AppState>) -> Result<Vec<Conversation>, String> {
    let conversations = state.conversations.lock().unwrap();
    let mut result: Vec<Conversation> = conversations.values().cloned().collect();
    
    // 按更新时间排序
    result.sort_by(|a, b| b.updated_at.cmp(&a.updated_at));
    
    Ok(result)
}

#[tauri::command]
async fn send_message(
    conversation_id: String,
    content: String,
    role: String,
    state: State<'_, AppState>,
) -> Result<Message, String> {
    let message = Message {
        id: Uuid::new_v4().to_string(),
        conversation_id: conversation_id.clone(),
        role,
        content,
        timestamp: Utc::now(),
        tokens: None,
        model: Some("gpt-3.5-turbo".to_string()),
    };

    // 保存消息
    let mut messages = state.messages.lock().unwrap();
    let conversation_messages = messages.entry(conversation_id.clone()).or_insert_with(Vec::new);
    conversation_messages.push(message.clone());

    // 更新对话信息
    let mut conversations = state.conversations.lock().unwrap();
    if let Some(conversation) = conversations.get_mut(&conversation_id) {
        conversation.message_count = conversation_messages.len() as u32;
        conversation.updated_at = Utc::now();
    }

    Ok(message)
}

#[tauri::command]
async fn get_messages(
    conversation_id: String,
    state: State<'_, AppState>,
) -> Result<Vec<Message>, String> {
    let messages = state.messages.lock().unwrap();
    let conversation_messages = messages.get(&conversation_id).cloned().unwrap_or_default();
    Ok(conversation_messages)
}

#[tauri::command]
async fn delete_conversation(
    conversation_id: String,
    state: State<'_, AppState>,
) -> Result<(), String> {
    let mut conversations = state.conversations.lock().unwrap();
    let mut messages = state.messages.lock().unwrap();
    
    conversations.remove(&conversation_id);
    messages.remove(&conversation_id);
    
    Ok(())
}

#[tauri::command]
async fn update_conversation(
    conversation_id: String,
    title: Option<String>,
    is_pinned: Option<bool>,
    is_archived: Option<bool>,
    state: State<'_, AppState>,
) -> Result<(), String> {
    let mut conversations = state.conversations.lock().unwrap();
    
    if let Some(conversation) = conversations.get_mut(&conversation_id) {
        if let Some(title) = title {
            conversation.title = title;
        }
        if let Some(is_pinned) = is_pinned {
            conversation.is_pinned = is_pinned;
        }
        if let Some(is_archived) = is_archived {
            conversation.is_archived = is_archived;
        }
        conversation.updated_at = Utc::now();
    }
    
    Ok(())
}

#[tauri::command]
async fn get_settings(state: State<'_, AppState>) -> Result<ChatSettings, String> {
    let settings = state.settings.lock().unwrap();
    Ok(settings.clone())
}

#[tauri::command]
async fn update_settings(
    new_settings: ChatSettings,
    state: State<'_, AppState>,
) -> Result<(), String> {
    let mut settings = state.settings.lock().unwrap();
    *settings = new_settings;
    Ok(())
}

// 模拟AI回复（开发阶段使用）
#[tauri::command]
async fn simulate_ai_response(
    conversation_id: String,
    user_message: String,
    state: State<'_, AppState>,
) -> Result<Message, String> {
    // 模拟处理时间
    tokio::time::sleep(tokio::time::Duration::from_millis(1000)).await;
    
    // 生成模拟回复
    let responses = vec![
        "这是一个很好的问题！让我来为你详细解答。",
        "根据你的描述，我建议采用以下几种方法：\n\n1. 首先分析问题的核心\n2. 制定解决方案\n3. 逐步实施",
        "我理解你的需求。这里有一些相关的信息可能对你有帮助。",
        "让我用代码示例来说明：\n\n```javascript\nfunction example() {\n  console.log(\"Hello, World!\");\n}\n```",
        "这个问题涉及多个方面，让我逐一为你分析：\n\n**方面一：** 技术实现\n**方面二：** 性能优化\n**方面三：** 用户体验",
    ];
    
    let response_content = responses[user_message.len() % responses.len()].to_string();
    
    let ai_message = Message {
        id: Uuid::new_v4().to_string(),
        conversation_id: conversation_id.clone(),
        role: "assistant".to_string(),
        content: response_content,
        timestamp: Utc::now(),
        tokens: Some(150),
        model: Some("gpt-3.5-turbo".to_string()),
    };

    // 保存AI回复
    let mut messages = state.messages.lock().unwrap();
    let conversation_messages = messages.entry(conversation_id.clone()).or_insert_with(Vec::new);
    conversation_messages.push(ai_message.clone());

    // 更新对话信息
    let mut conversations = state.conversations.lock().unwrap();
    if let Some(conversation) = conversations.get_mut(&conversation_id) {
        conversation.message_count = conversation_messages.len() as u32;
        conversation.updated_at = Utc::now();
    }

    Ok(ai_message)
}

// 分组管理命令
#[tauri::command]
async fn create_group(
    name: String,
    color: Option<String>,
    icon: Option<String>,
    state: State<'_, AppState>,
) -> Result<ConversationGroup, String> {
    let group = ConversationGroup {
        id: Uuid::new_v4().to_string(),
        name,
        color,
        icon,
        created_at: Utc::now(),
        updated_at: Utc::now(),
        conversation_count: 0,
        is_collapsed: Some(false),
        sort_order: {
            let groups = state.conversation_groups.lock().unwrap();
            groups.len() as u32
        },
    };

    let mut groups = state.conversation_groups.lock().unwrap();
    groups.insert(group.id.clone(), group.clone());

    Ok(group)
}

#[tauri::command]
async fn get_groups(state: State<'_, AppState>) -> Result<Vec<ConversationGroup>, String> {
    let groups = state.conversation_groups.lock().unwrap();
    let mut result: Vec<ConversationGroup> = groups.values().cloned().collect();

    // 按排序顺序排序
    result.sort_by(|a, b| a.sort_order.cmp(&b.sort_order));

    Ok(result)
}

#[tauri::command]
async fn update_group(
    group_id: String,
    name: Option<String>,
    color: Option<String>,
    icon: Option<String>,
    is_collapsed: Option<bool>,
    state: State<'_, AppState>,
) -> Result<(), String> {
    let mut groups = state.conversation_groups.lock().unwrap();

    if let Some(group) = groups.get_mut(&group_id) {
        if let Some(name) = name {
            group.name = name;
        }
        if let Some(color) = color {
            group.color = Some(color);
        }
        if let Some(icon) = icon {
            group.icon = Some(icon);
        }
        if let Some(is_collapsed) = is_collapsed {
            group.is_collapsed = Some(is_collapsed);
        }
        group.updated_at = Utc::now();
    }

    Ok(())
}

#[tauri::command]
async fn delete_group(
    group_id: String,
    state: State<'_, AppState>,
) -> Result<(), String> {
    // 将分组内的对话移到未分组
    let mut conversations = state.conversations.lock().unwrap();
    for conversation in conversations.values_mut() {
        if conversation.group_id.as_ref() == Some(&group_id) {
            conversation.group_id = None;
        }
    }

    // 删除分组
    let mut groups = state.conversation_groups.lock().unwrap();
    groups.remove(&group_id);

    Ok(())
}

#[tauri::command]
async fn move_conversation_to_group(
    conversation_id: String,
    group_id: Option<String>,
    state: State<'_, AppState>,
) -> Result<(), String> {
    let mut conversations = state.conversations.lock().unwrap();

    if let Some(conversation) = conversations.get_mut(&conversation_id) {
        conversation.group_id = group_id;
        conversation.updated_at = Utc::now();
    }

    Ok(())
}

fn main() {
    // 初始化应用状态
    let app_state = AppState {
        conversations: Mutex::new(HashMap::new()),
        conversation_groups: Mutex::new(HashMap::new()),
        messages: Mutex::new(HashMap::new()),
        settings: Mutex::new(ChatSettings {
            temperature: 0.7,
            max_tokens: 2048,
            top_p: 1.0,
            frequency_penalty: 0.0,
            presence_penalty: 0.0,
            system_prompt: Some("你是一个有用的AI助手，请用中文回答问题。".to_string()),
        }),
    };

    tauri::Builder::default()
        .manage(app_state)
        .invoke_handler(tauri::generate_handler![
            create_conversation,
            get_conversations,
            send_message,
            get_messages,
            delete_conversation,
            update_conversation,
            get_settings,
            update_settings,
            simulate_ai_response,
            create_group,
            get_groups,
            update_group,
            delete_group,
            move_conversation_to_group
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
