<template>
  <div class="model-settings">
    <div class="settings-section">
      <div class="section-header">
        <h4>模型参数</h4>
        <p>调整AI模型的生成参数</p>
      </div>
      
      <div class="section-content">
        <div class="setting-item">
          <div class="setting-label">
            <label>温度 ({{ chatSettings.temperature }})</label>
            <small>控制回答的随机性，值越高越有创意</small>
          </div>
          <div class="setting-control">
            <n-slider
              v-model:value="chatSettings.temperature"
              :min="0"
              :max="2"
              :step="0.1"
              @update:value="updateChatSettings"
            />
          </div>
        </div>

        <div class="setting-item">
          <div class="setting-label">
            <label>最大Token数</label>
            <small>单次回复的最大长度</small>
          </div>
          <div class="setting-control">
            <n-input-number
              v-model:value="chatSettings.maxTokens"
              :min="100"
              :max="8000"
              @update:value="updateChatSettings"
            />
          </div>
        </div>

        <div class="setting-item">
          <div class="setting-label">
            <label>Top P ({{ chatSettings.topP }})</label>
            <small>核采样参数，控制词汇选择范围</small>
          </div>
          <div class="setting-control">
            <n-slider
              v-model:value="chatSettings.topP"
              :min="0"
              :max="1"
              :step="0.1"
              @update:value="updateChatSettings"
            />
          </div>
        </div>

        <div class="setting-item">
          <div class="setting-label">
            <label>频率惩罚 ({{ chatSettings.frequencyPenalty }})</label>
            <small>减少重复内容的程度</small>
          </div>
          <div class="setting-control">
            <n-slider
              v-model:value="chatSettings.frequencyPenalty"
              :min="0"
              :max="2"
              :step="0.1"
              @update:value="updateChatSettings"
            />
          </div>
        </div>

        <div class="setting-item">
          <div class="setting-label">
            <label>存在惩罚 ({{ chatSettings.presencePenalty }})</label>
            <small>鼓励谈论新话题的程度</small>
          </div>
          <div class="setting-control">
            <n-slider
              v-model:value="chatSettings.presencePenalty"
              :min="0"
              :max="2"
              :step="0.1"
              @update:value="updateChatSettings"
            />
          </div>
        </div>

        <div class="setting-item full-width">
          <div class="setting-label">
            <label>系统提示词</label>
            <small>定义AI助手的角色和行为</small>
          </div>
          <div class="setting-control full">
            <n-input
              v-model:value="chatSettings.systemPrompt"
              type="textarea"
              :autosize="{ minRows: 3, maxRows: 6 }"
              placeholder="输入系统提示词..."
              @update:value="updateChatSettings"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useMessage } from 'naive-ui'
import { useSettingsStore } from '@/stores/settings'

const message = useMessage()
const settingsStore = useSettingsStore()

// 计算属性
const chatSettings = computed(() => settingsStore.chat)

// 方法
const updateChatSettings = () => {
  settingsStore.saveSettings()
  message.success('模型参数已更新')
}
</script>

<style scoped>
.model-settings {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.settings-section {
  background: white;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  overflow: hidden;
}

.section-header {
  padding: 20px 24px;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

.section-header h4 {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 4px 0;
}

.section-header p {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
}

.section-content {
  padding: 24px;
}

.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 0;
  border-bottom: 1px solid #f1f5f9;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-item.full-width {
  flex-direction: column;
  align-items: flex-start;
  gap: 12px;
}

.setting-label {
  flex: 1;
  margin-right: 24px;
}

.setting-item.full-width .setting-label {
  margin-right: 0;
}

.setting-label label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 4px;
}

.setting-label small {
  font-size: 12px;
  color: #9ca3af;
  line-height: 1.4;
}

.setting-control {
  flex: 0 0 200px;
  display: flex;
  justify-content: flex-end;
}

.setting-control.full {
  flex: none;
  width: 100%;
  justify-content: flex-start;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .setting-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .setting-label {
    margin-right: 0;
  }
  
  .setting-control {
    flex: none;
    width: 100%;
    justify-content: flex-start;
  }
}
</style>
