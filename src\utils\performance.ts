// 性能优化工具类

// LRU缓存实现
export class LRUCache<K, V> {
  private cache = new Map<K, V>()
  private _maxSize: number

  constructor(maxSize: number) {
    this._maxSize = maxSize
  }

  get(key: K): V | undefined {
    const value = this.cache.get(key)
    if (value !== undefined) {
      // 移到最前面
      this.cache.delete(key)
      this.cache.set(key, value)
    }
    return value
  }

  set(key: K, value: V): void {
    if (this.cache.has(key)) {
      this.cache.delete(key)
    } else if (this.cache.size >= this._maxSize) {
      // 删除最久未使用的项
      const firstKey = this.cache.keys().next().value
      this.cache.delete(firstKey)
    }
    
    this.cache.set(key, value)
  }

  clear(): void {
    this.cache.clear()
  }

  has(key: K): boolean {
    return this.cache.has(key)
  }

  delete(key: K): boolean {
    return this.cache.delete(key)
  }

  get size(): number {
    return this.cache.size
  }

  get maxSize(): number {
    return this._maxSize
  }
}

// 性能监控类
export class PerformanceMonitor {
  private metrics = new Map<string, number>()

  startTiming(name: string): void {
    performance.mark(`${name}-start`)
  }

  endTiming(name: string): number {
    performance.mark(`${name}-end`)
    performance.measure(name, `${name}-start`, `${name}-end`)
    
    const measure = performance.getEntriesByName(name, 'measure')[0]
    this.metrics.set(name, measure.duration)
    
    // 清理标记
    performance.clearMarks(`${name}-start`)
    performance.clearMarks(`${name}-end`)
    performance.clearMeasures(name)
    
    return measure.duration
  }

  getMetrics(): Record<string, number> {
    return Object.fromEntries(this.metrics)
  }

  clearMetrics(): void {
    this.metrics.clear()
  }
}

// 内存使用监控
export function monitorMemoryUsage() {
  if ('memory' in performance) {
    const memory = (performance as any).memory

    return {
      used: memory.usedJSHeapSize,
      total: memory.totalJSHeapSize,
      limit: memory.jsHeapSizeLimit,
      usage: memory.usedJSHeapSize / memory.jsHeapSizeLimit,
      usagePercent: Math.round((memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100),
      available: memory.jsHeapSizeLimit - memory.usedJSHeapSize
    }
  }

  return null
}

// 性能监控
export function monitorPerformance() {
  try {
    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
    const memory = monitorMemoryUsage()

    return {
      memory,
      timing: {
        domContentLoaded: navigation ? navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart : 0,
        loadComplete: navigation ? navigation.loadEventEnd - navigation.loadEventStart : 0,
        firstPaint: performance.getEntriesByName('first-paint')[0]?.startTime || 0,
        firstContentfulPaint: performance.getEntriesByName('first-contentful-paint')[0]?.startTime || 0
      },
      connection: (navigator as any).connection ? {
        effectiveType: (navigator as any).connection.effectiveType,
        downlink: (navigator as any).connection.downlink,
        rtt: (navigator as any).connection.rtt
      } : null
    }
  } catch (error) {
    return null
  }
}

// 防抖函数
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null
  
  return (...args: Parameters<T>) => {
    if (timeout) {
      clearTimeout(timeout)
    }
    
    timeout = setTimeout(() => {
      func(...args)
    }, wait)
  }
}

// 节流函数
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let lastTime = 0
  
  return (...args: Parameters<T>) => {
    const now = Date.now()
    
    if (now - lastTime >= wait) {
      lastTime = now
      func(...args)
    }
  }
}

// 分块处理函数
export function processInChunks<T>(
  items: T[],
  chunkSize: number,
  processor: (chunk: T[]) => Promise<void>
): Promise<void> {
  return new Promise((resolve) => {
    let index = 0
    
    function processNextChunk() {
      const chunk = items.slice(index, index + chunkSize)
      
      if (chunk.length === 0) {
        resolve()
        return
      }
      
      processor(chunk).then(() => {
        index += chunkSize
        // 使用 requestIdleCallback 或 setTimeout 来避免阻塞主线程
        if ('requestIdleCallback' in window) {
          requestIdleCallback(processNextChunk)
        } else {
          setTimeout(processNextChunk, 0)
        }
      })
    }
    
    processNextChunk()
  })
}

// 内存清理
export function cleanupMemory() {
  // 清理不必要的缓存
  if (window.renderCache) {
    window.renderCache.clear()
  }
  
  // 强制垃圾回收（开发环境）
  if (process.env.NODE_ENV === 'development' && 'gc' in window) {
    (window as any).gc()
  }
}

// 检测设备性能
export function detectDevicePerformance() {
  const canvas = document.createElement('canvas')
  const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl')
  
  let performanceLevel = 'medium'
  
  // 检测硬件加速
  if (gl) {
    const debugInfo = gl.getExtension('WEBGL_debug_renderer_info')
    if (debugInfo) {
      const renderer = gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL)
      // 简单的GPU性能检测
      if (renderer.includes('Intel') || renderer.includes('AMD')) {
        performanceLevel = 'high'
      }
    }
  }
  
  // 检测内存
  const memory = monitorMemoryUsage()
  if (memory && memory.limit < 1024 * 1024 * 1024) { // 小于1GB
    performanceLevel = 'low'
  }
  
  // 检测CPU核心数
  if (navigator.hardwareConcurrency && navigator.hardwareConcurrency < 4) {
    performanceLevel = 'low'
  }
  
  return {
    level: performanceLevel,
    memory,
    cores: navigator.hardwareConcurrency || 1,
    webgl: !!gl
  }
}