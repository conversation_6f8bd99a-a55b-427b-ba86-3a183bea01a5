import { defineConfig, presetUno, presetIcons } from 'unocss'

export default defineConfig({
  presets: [
    presetUno(),
    presetIcons({
      collections: {
        carbon: () => import('@iconify/json/json/carbon.json').then(i => i.default),
        mdi: () => import('@iconify/json/json/mdi.json').then(i => i.default),
        lucide: () => import('@iconify/json/json/lucide.json').then(i => i.default),
        tabler: () => import('@iconify/json/json/tabler.json').then(i => i.default)
      }
    })
  ],
  shortcuts: {
    // 按钮样式
    'btn-primary': 'bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors cursor-pointer',
    'btn-secondary': 'bg-gray-200 hover:bg-gray-300 text-gray-800 px-4 py-2 rounded-lg transition-colors cursor-pointer',
    'btn-ghost': 'hover:bg-gray-100 text-gray-700 px-4 py-2 rounded-lg transition-colors cursor-pointer',
    
    // 卡片样式
    'card': 'bg-white rounded-lg shadow-sm border border-gray-200 p-4',
    'card-hover': 'bg-white rounded-lg shadow-sm border border-gray-200 p-4 hover:shadow-md transition-shadow cursor-pointer',
    
    // 输入框样式
    'input-base': 'border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent',
    
    // 布局样式
    'flex-center': 'flex items-center justify-center',
    'flex-between': 'flex items-center justify-between',
    'flex-col-center': 'flex flex-col items-center justify-center',
    
    // 文本样式
    'text-title': 'text-xl font-semibold text-gray-900',
    'text-subtitle': 'text-lg font-medium text-gray-700',
    'text-body': 'text-sm text-gray-600',
    'text-caption': 'text-xs text-gray-500',
    
    // 状态样式
    'status-success': 'bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium',
    'status-warning': 'bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-xs font-medium',
    'status-error': 'bg-red-100 text-red-800 px-2 py-1 rounded-full text-xs font-medium',
    'status-info': 'bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium',
    
    // 动画样式
    'animate-fade-in': 'animate-fade-in',
    'animate-slide-up': 'animate-slide-up',
    'animate-bounce-in': 'animate-bounce-in'
  },
  rules: [
    // 自定义规则
    [/^animate-fade-in$/, () => ({
      'animation': 'fadeIn 0.3s ease-in-out'
    })],
    [/^animate-slide-up$/, () => ({
      'animation': 'slideUp 0.3s ease-out'
    })],
    [/^animate-bounce-in$/, () => ({
      'animation': 'bounceIn 0.5s ease-out'
    })]
  ],
  theme: {
    colors: {
      primary: {
        50: '#eff6ff',
        100: '#dbeafe',
        200: '#bfdbfe',
        300: '#93c5fd',
        400: '#60a5fa',
        500: '#3b82f6',
        600: '#2563eb',
        700: '#1d4ed8',
        800: '#1e40af',
        900: '#1e3a8a'
      },
      gray: {
        50: '#f9fafb',
        100: '#f3f4f6',
        200: '#e5e7eb',
        300: '#d1d5db',
        400: '#9ca3af',
        500: '#6b7280',
        600: '#4b5563',
        700: '#374151',
        800: '#1f2937',
        900: '#111827'
      }
    },
    fontFamily: {
      sans: ['-apple-system', 'BlinkMacSystemFont', '"Segoe UI"', 'Roboto', '"Helvetica Neue"', 'Arial', 'sans-serif'],
      mono: ['"SF Mono"', 'Monaco', '"Cascadia Code"', '"Roboto Mono"', 'Consolas', '"Courier New"', 'monospace']
    },
    animation: {
      keyframes: {
        fadeIn: '{from{opacity:0}to{opacity:1}}',
        slideUp: '{from{transform:translateY(20px);opacity:0}to{transform:translateY(0);opacity:1}}',
        bounceIn: '{0%{transform:scale(0.3);opacity:0}50%{transform:scale(1.05)}70%{transform:scale(0.9)}100%{transform:scale(1);opacity:1}}'
      }
    }
  },
  safelist: [
    // 确保这些类不会被清除
    'animate-fade-in',
    'animate-slide-up',
    'animate-bounce-in',
    'btn-primary',
    'btn-secondary',
    'card',
    'input-base'
  ]
})
